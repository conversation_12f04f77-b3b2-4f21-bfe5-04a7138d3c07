# BIOS Terminal GUI Launcher

## 🎯 项目概述

这是一个图形界面的启动器程序，提供类似 `start.bat` 的功能，但具有更友好的用户界面。它可以启动和管理你的BIOS风格终端程序。

## 📁 文件说明

### GUI启动器文件
- **`bios_terminal_launcher.py`** - 完整功能的GUI启动器
- **`simple_launcher.py`** - 简化版GUI启动器（推荐测试用）

### 打包相关文件
- **`build_exe.py`** - 自动化打包脚本
- **`build.bat`** - 一键构建批处理文件

### 核心程序文件（需要存在）
- **`stable_bios_terminal.py`** - 主BIOS终端程序
- **`demo_program1.py`** / **`demo_program2.py`** - 示例程序

## 🚀 使用方法

### 方法1：直接运行Python版本
```bash
# 运行完整版GUI启动器
python bios_terminal_launcher.py

# 或运行简化版（推荐测试）
python simple_launcher.py
```

### 方法2：构建exe文件
```bash
# 一键构建（推荐）
build.bat

# 或手动构建
python build_exe.py
```

构建完成后，在 `release` 文件夹中会生成：
- `BIOS_Terminal_Launcher.exe` - 独立可执行文件
- 其他必要的程序文件

## 🎮 GUI功能特点

### 简化版启动器 (`simple_launcher.py`)
- ✅ **BIOS风格界面** - 蓝色背景，经典字体
- ✅ **一键启动** - 点击按钮启动BIOS终端
- ✅ **进程监控** - 自动检测程序状态
- ✅ **简单控制** - 启动/停止按钮
- ✅ **状态显示** - 实时显示程序状态

### 完整版启动器 (`bios_terminal_launcher.py`)
- ✅ **系统信息显示** - CPU、内存、系统版本等
- ✅ **实时状态监控** - 运行时间、最后活动时间
- ✅ **输出日志显示** - 实时显示终端程序输出
- ✅ **详细控制面板** - 更多控制选项
- ✅ **自动刷新** - 定时更新状态信息

## 🖥️ 界面预览

### 简化版界面布局
```
┌─────────────────────────────────────┐
│        BIOS TERMINAL LAUNCHER       │
├─────────────────────────────────────┤
│ System Ready                        │
│ Current Time: 2024-XX-XX XX:XX:XX   │
├─────────────────────────────────────┤
│        [Start BIOS Terminal]        │
│        [Stop Terminal]              │
├─────────────────────────────────────┤
│ Status: Ready to start              │
├─────────────────────────────────────┤
│ Instructions:                       │
│ • Click 'Start' to launch terminal  │
│ • Use arrow keys to navigate        │
│ • Press Enter to execute            │
│ • Press ESC or Q to exit            │
└─────────────────────────────────────┘
```

## 🔧 技术实现

### 核心技术
- **tkinter** - Python标准GUI库
- **subprocess** - 进程管理
- **threading** - 多线程处理
- **PyInstaller** - exe打包工具

### 主要功能
1. **进程管理** - 启动、停止、监控BIOS终端程序
2. **状态监控** - 实时显示程序运行状态
3. **输出捕获** - 捕获并显示终端程序输出
4. **错误处理** - 友好的错误提示和异常处理

## 📦 打包为exe

### 自动打包（推荐）
```bash
# 运行自动构建脚本
build.bat
```

### 手动打包
```bash
# 1. 安装依赖
pip install pyinstaller psutil

# 2. 运行构建脚本
python build_exe.py

# 3. 查看结果
# exe文件位于: dist/BIOS_Terminal_Launcher.exe
# 便携包位于: release/ 文件夹
```

### 打包配置
- **无控制台窗口** - 纯GUI应用
- **包含所有依赖** - 独立运行，无需Python环境
- **自动包含资源** - 包含所有必要的程序文件
- **便携式包** - 包含使用说明和所有文件

## 🎯 使用场景

### 适合的用户
- 喜欢图形界面的用户
- 需要监控程序状态的用户
- 想要一键启动的用户
- 需要分发给其他人使用的场景

### 优势对比
| 特性 | start.bat | GUI启动器 |
|------|-----------|-----------|
| 启动方式 | 命令行 | 图形界面 |
| 状态监控 | 无 | 实时监控 |
| 输出显示 | 无 | 日志窗口 |
| 用户友好性 | 一般 | 优秀 |
| 分发便利性 | 需要Python | 独立exe |

## 🔍 故障排除

### 常见问题

1. **GUI无法启动**
   - 检查Python版本（需要3.6+）
   - 确保tkinter可用：`python -c "import tkinter"`

2. **找不到stable_bios_terminal.py**
   - 确保文件在同一目录下
   - 检查文件名拼写

3. **exe构建失败**
   - 安装PyInstaller：`pip install pyinstaller`
   - 检查所有依赖文件是否存在

4. **程序无法启动BIOS终端**
   - 检查Python路径
   - 确保有足够的系统权限

### 调试模式
```bash
# 在命令行中运行，查看详细错误信息
python simple_launcher.py
```

## 🌟 扩展建议

如果需要进一步定制，可以考虑：

1. **添加图标** - 为exe文件添加自定义图标
2. **主题定制** - 支持不同的颜色主题
3. **配置文件** - 支持自定义启动参数
4. **多语言支持** - 添加中英文切换
5. **自动更新** - 检查程序更新功能

## 📝 总结

GUI启动器为你的BIOS终端程序提供了：
- 🎮 **友好的图形界面**
- 📊 **实时状态监控**
- 🚀 **一键启动功能**
- 📦 **独立exe分发**
- 🔧 **简单易用的控制**

现在你可以选择使用命令行版本（`start.bat`）或图形界面版本，享受更好的用户体验！
