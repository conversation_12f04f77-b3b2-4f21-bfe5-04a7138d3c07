#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple BIOS Terminal GUI Launcher
简化版图形界面启动器
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import os
import sys
from datetime import datetime

class SimpleLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BIOS Terminal Launcher")
        self.root.geometry("600x400")
        self.root.configure(bg='#000080')  # BIOS蓝色
        
        self.terminal_process = None
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = tk.Label(self.root, text="BIOS TERMINAL LAUNCHER", 
                              bg='#000080', fg='yellow', 
                              font=('Consolas', 16, 'bold'))
        title_label.pack(pady=20)
        
        # 信息框
        info_frame = tk.Frame(self.root, bg='#000080')
        info_frame.pack(pady=10, padx=20, fill=tk.X)
        
        tk.Label(info_frame, text="System Ready", 
                bg='#000080', fg='white', 
                font=('Consolas', 10)).pack()
        
        tk.Label(info_frame, text=f"Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", 
                bg='#000080', fg='cyan', 
                font=('Consolas', 9)).pack()
        
        # 按钮区域
        button_frame = tk.Frame(self.root, bg='#000080')
        button_frame.pack(pady=30)
        
        # 启动按钮
        self.start_btn = tk.Button(button_frame, text="Start BIOS Terminal", 
                                  command=self.start_terminal,
                                  bg='#008000', fg='white', 
                                  font=('Consolas', 12, 'bold'),
                                  width=20, height=2)
        self.start_btn.pack(pady=10)
        
        # 停止按钮
        self.stop_btn = tk.Button(button_frame, text="Stop Terminal", 
                                 command=self.stop_terminal,
                                 bg='#800000', fg='white', 
                                 font=('Consolas', 12, 'bold'),
                                 width=20, height=2,
                                 state=tk.DISABLED)
        self.stop_btn.pack(pady=10)
        
        # 状态显示
        self.status_label = tk.Label(self.root, text="Ready to start BIOS Terminal", 
                                    bg='#000080', fg='white', 
                                    font=('Consolas', 10))
        self.status_label.pack(pady=20)
        
        # 说明文本
        help_text = """
Instructions:
• Click 'Start BIOS Terminal' to launch the terminal program
• Use arrow keys in terminal to navigate menu
• Press Enter to execute menu items
• Press ESC or Q to exit terminal
• Click 'Stop Terminal' to force close if needed
        """
        
        help_label = tk.Label(self.root, text=help_text, 
                             bg='#000080', fg='lightgray', 
                             font=('Consolas', 8),
                             justify=tk.LEFT)
        help_label.pack(pady=10)
        
    def start_terminal(self):
        """启动BIOS终端"""
        try:
            if not os.path.exists("stable_bios_terminal.py"):
                messagebox.showerror("Error", "stable_bios_terminal.py not found!")
                return
                
            self.status_label.config(text="Starting BIOS Terminal...")
            self.root.update()
            
            # 启动终端程序
            self.terminal_process = subprocess.Popen([sys.executable, "stable_bios_terminal.py"])
            
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            self.status_label.config(text="BIOS Terminal is running")
            
            # 监控进程状态
            self.monitor_process()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start terminal: {e}")
            self.status_label.config(text="Failed to start")
            
    def stop_terminal(self):
        """停止BIOS终端"""
        try:
            if self.terminal_process:
                self.terminal_process.terminate()
                self.terminal_process = None
                
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.status_label.config(text="Terminal stopped")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop terminal: {e}")
            
    def monitor_process(self):
        """监控进程状态"""
        if self.terminal_process:
            if self.terminal_process.poll() is not None:
                # 进程已结束
                self.start_btn.config(state=tk.NORMAL)
                self.stop_btn.config(state=tk.DISABLED)
                self.status_label.config(text="Terminal exited")
                self.terminal_process = None
                return
                
        # 每秒检查一次
        self.root.after(1000, self.monitor_process)
        
    def on_closing(self):
        """程序关闭时清理"""
        if self.terminal_process:
            self.terminal_process.terminate()
        self.root.destroy()
        
    def run(self):
        """运行GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """主函数"""
    try:
        launcher = SimpleLauncher()
        launcher.run()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
