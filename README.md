# BIOS风格Windows终端程序

这是一个使用Python开发的BIOS风格终端程序，完全满足你的需求：

✅ **左侧控制菜单** - 完整的程序控制界面
✅ **右侧上下两个输出窗口** - 分别显示两个程序的实时输出
✅ **类似BIOS界面的蓝色主题** - 经典的BIOS风格设计
✅ **同时运行和监控两个子程序** - 多线程异步处理

## 📁 项目文件说明

### 🌟 核心文件
- **`stable_bios_terminal.py`** - 🏆 **主程序** - 完整的BIOS风格终端程序
- **`start.bat`** - 🚀 **启动脚本** - 一键启动程序
- **`demo_program1.py`** / **`demo_program2.py`** - 示例子程序
- **`requirements.txt`** - 依赖包列表

### 📖 文档文件
- **`README.md`** - 使用说明（本文件）
- **`OVERALL窗口功能说明.md`** - OVERALL窗口详细说明
- **`项目总结.md`** - 项目开发总结

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install windows-curses psutil
```

### 2. 运行程序

#### 🏆 推荐方法：使用启动脚本
```bash
# 双击 start.bat 文件
# 或在命令行中运行：
start.bat
```

#### 🌟 直接运行Python
```bash
python stable_bios_terminal.py
```

### 3. 操作说明
- **↑/↓ 方向键**: 在菜单中上下移动选择
- **回车键**: 执行选中的菜单项
- **ESC键 或 Q键**: 退出程序

## 📋 功能菜单

### 主界面程序（在原窗口显示）
1. **Start Program 1** - 启动程序1（计数器演示）
2. **Start Program 2** - 启动程序2（时间显示演示）
3. **Stop Program 1** - 停止程序1
4. **Stop Program 2** - 停止程序2
5. **Clear Output 1** - 清空程序1的输出窗口
6. **Clear Output 2** - 清空程序2的输出窗口

### 🆕 独立窗口程序（新窗口运行）
7. **Start Program 3 (New Window)** - 启动网络监控程序（独立窗口）
8. **Start Program 4 (New Window)** - 启动文件监控程序（独立窗口）
9. **Stop Program 3** - 停止程序3
10. **Stop Program 4** - 停止程序4

### 系统功能
11. **System Info** - 显示系统信息
12. **Exit** - 退出程序

## ✨ 界面预览

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                    BIOS TERMINAL - PYTHON VERSION                          │
├─────────────────────┬───────────────────────────────────────────────────────┤
│ CONTROL MENU        │ 🆕 OVERALL INFO (PROGRAM 0)                          │
│                     │                                                       │
│ > Start Program 1   │ [15:37:34] BIOS Terminal System Ready                 │
│   Start Program 2   │ [15:37:34] program1 started successfully              │
│   Stop Program 1    │ [15:37:34] System operations and status info         │
│   Stop Program 2    ├───────────────────────────────────────────────────────┤
│   Clear Output 1    │ PROGRAM 1 OUTPUT                                     │
│   Clear Output 2    │                                                       │
│   Start Program 3   │ [15:37:34] Counter: 0                                │
│   Start Program 4   │ [15:37:35] Counter: 1                                │
│   Stop Program 3    │ [15:37:36] Counter: 2                                │
│   Stop Program 4    ├───────────────────────────────────────────────────────┤
│   System Info       │ PROGRAM 2 OUTPUT                                     │
│   Exit              │                                                       │
│                     │ [15:37:34] Time: 15:37:34                            │
│                     │ [15:37:36] Time: 15:37:36                            │
└─────────────────────┴───────────────────────────────────────────────────────┤
│ UP/DOWN:Select ENTER:Execute R:Refresh ESC:Exit | P1:RUN P2:RUN P3:STOP P4:STOP │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 🎯 功能特性

### 界面布局
- **🔵 蓝色标题栏**: 经典BIOS风格的标题显示
- **📋 左侧控制菜单**: 完整的程序控制面板，支持键盘导航
- **🆕 右上OVERALL窗口**: 显示系统操作反馈和状态信息（PROGRAM 0）
- **📺 右中程序1窗口**: 专门显示程序1的输出，保持纯净
- **📺 右下程序2窗口**: 专门显示程序2的输出，保持纯净
- **📊 底部状态栏**: 显示操作提示和所有程序运行状态

### 核心功能
- ✅ **多程序管理**: 同时运行和监控最多4个独立的子程序
- ✅ **双显示模式**: 2个程序在主界面显示 + 2个程序在独立窗口运行
- ✅ **实时输出**: 异步显示程序输出，不阻塞界面操作
- ✅ **稳定显示**: 修复了闪烁问题，界面稳定流畅
- ✅ **智能刷新**: 只在必要时更新界面，减少资源消耗
- ✅ **安全退出**: 自动清理子进程，避免僵尸进程

### 示例程序
#### 主界面程序
- **内置计数器**: 演示程序1，每秒输出递增计数
- **时间显示器**: 演示程序2，每2秒显示当前时间

#### 🆕 独立窗口程序
- **网络监控器**: 程序3，模拟网络状态监控（下载/上传速度、延迟、数据包统计）
- **文件监控器**: 程序4，模拟文件系统监控（文件操作事件、大小信息）

#### 系统功能
- **系统信息**: 显示操作系统、Python版本等信息

## 📦 安装要求

### Python版本
- Python 3.6 或更高版本

### 依赖包
```bash
pip install -r requirements.txt
```

主要依赖：
- `windows-curses`: Windows下的curses库支持
- `psutil`: 系统信息获取（用于示例程序）

## 🎮 操作说明

### 基本操作
- **↑/↓ 方向键**: 在菜单中上下移动
- **回车键**: 执行选中的菜单项
- **ESC键 或 Q键**: 退出程序

### 菜单功能
1. **Start Program 1**: 启动计数器程序
2. **Start Program 2**: 启动时间显示程序
3. **Stop Program 1/2**: 停止对应的程序
4. **Clear Output 1/2**: 清空对应窗口的输出
5. **System Info**: 显示当前系统信息
6. **Exit**: 退出程序

## 自定义程序

你可以通过修改 `bios_terminal.py` 中的以下部分来运行自己的程序：

```python
def handle_menu_action(self):
    if action == "启动程序1":
        # 修改这里的命令来运行你的程序
        self.start_program('program1', 'python your_program1.py')
    elif action == "启动程序2":
        # 修改这里的命令来运行你的程序
        self.start_program('program2', 'python your_program2.py')
```

## 技术实现

### 核心技术
- **curses库**: 用于创建文本用户界面
- **threading**: 多线程处理子程序输出
- **subprocess**: 启动和管理子进程
- **queue**: 线程安全的输出队列

### 架构特点
- 异步输出处理，不阻塞主界面
- 实时更新显示
- 自动滚动输出窗口
- 优雅的程序退出和资源清理

## 注意事项

1. **Windows兼容性**: 需要安装 `windows-curses` 包
2. **终端大小**: 建议使用较大的终端窗口以获得最佳显示效果
3. **程序路径**: 确保子程序文件在正确的路径下
4. **编码问题**: 如果遇到中文显示问题，请确保终端支持UTF-8编码

## 故障排除

### 常见问题

1. **ImportError: No module named '_curses'**
   - 解决方案: `pip install windows-curses`

2. **程序无法启动**
   - 检查Python版本是否符合要求
   - 确保所有依赖包已正确安装

3. **中文显示乱码**
   - 设置终端编码为UTF-8
   - 在Windows命令提示符中运行: `chcp 65001`

4. **子程序无法启动**
   - 检查程序文件路径是否正确
   - 确保Python环境配置正确

## 扩展建议

- 添加更多菜单选项
- 支持配置文件
- 添加日志记录功能
- 支持更多的输出格式
- 添加程序性能监控
- 支持远程程序执行
