@echo off
echo BIOS Terminal Launcher - Auto Build Script
echo ==========================================
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo Error: Python not found! Please install Python first.
    pause
    exit /b 1
)

echo.
echo Installing required packages...
pip install pyinstaller psutil

echo.
echo Building executable...
python build_exe.py

echo.
echo Build process completed!
echo Check the 'release' folder for the portable package.
echo.
pause
