#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultra Minimal Launcher
超级简洁启动器 - 直接启动，无任何警告信息
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import os
import sys
from datetime import datetime

class UltraMinimalLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BIOS Terminal")
        self.root.geometry("400x200")
        self.root.resizable(False, False)
        
        # 极简配色
        self.colors = {
            'bg': '#1A1A1A',           # 深黑背景
            'text': '#FFFFFF',         # 纯白文字
            'accent': '#00D4AA',       # 青绿强调色
            'button': '#007ACC',       # 蓝色按钮
            'button_hover': '#005A9E', # 按钮悬停
            'success': '#4CAF50',      # 成功绿色
            'muted': '#888888'         # 静音灰色
        }
        
        self.terminal_process = None
        
        self.setup_window()
        self.create_ui()
        
    def setup_window(self):
        """设置窗口"""
        self.root.configure(bg=self.colors['bg'])
        
        # 居中显示
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 400) // 2
        y = (screen_height - 200) // 2
        self.root.geometry(f"400x200+{x}+{y}")
        
    def create_ui(self):
        """创建超简洁界面"""
        # 主容器
        container = tk.Frame(self.root, bg=self.colors['bg'])
        container.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # 标题
        title = tk.Label(container, 
                        text="BIOS Terminal",
                        bg=self.colors['bg'], 
                        fg=self.colors['text'],
                        font=('Segoe UI', 18, 'bold'))
        title.pack(pady=(0, 30))
        
        # 启动按钮
        self.start_button = tk.Button(container,
                                     text="▶ Launch",
                                     command=self.launch_terminal,
                                     bg=self.colors['button'],
                                     fg='white',
                                     font=('Segoe UI', 14, 'bold'),
                                     relief=tk.FLAT,
                                     bd=0,
                                     height=2,
                                     cursor='hand2')
        self.start_button.pack(fill=tk.X, pady=(0, 20))
        
        # 状态
        self.status = tk.Label(container, 
                              text="Ready",
                              bg=self.colors['bg'], 
                              fg=self.colors['muted'],
                              font=('Segoe UI', 10))
        self.status.pack()
        
        # 时间
        self.time_label = tk.Label(container, 
                                  text="",
                                  bg=self.colors['bg'], 
                                  fg=self.colors['accent'],
                                  font=('Segoe UI', 9))
        self.time_label.pack(side=tk.BOTTOM, pady=(20, 0))
        
        # 按钮效果
        self.start_button.bind("<Enter>", lambda e: self.start_button.config(bg=self.colors['button_hover']))
        self.start_button.bind("<Leave>", lambda e: self.start_button.config(bg=self.colors['button']))
        
        # 启动时间更新
        self.update_time()
        
    def update_time(self):
        """更新时间"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def launch_terminal(self):
        """直接启动终端，无任何警告"""
        try:
            if not os.path.exists("stable_bios_terminal.py"):
                messagebox.showerror("Error", "stable_bios_terminal.py not found!")
                return
                
            # 更新状态
            self.status.config(text="Launching...", fg=self.colors['accent'])
            self.start_button.config(text="⏳ Starting...", state=tk.DISABLED, 
                                   bg=self.colors['muted'])
            self.root.update()
            
            # 直接启动，无延迟，无警告
            self.terminal_process = subprocess.Popen([sys.executable, "stable_bios_terminal.py"])
            
            # 更新为运行状态
            self.start_button.config(text="✓ Running", bg=self.colors['success'])
            self.status.config(text="Terminal Active", fg=self.colors['success'])
            
            # 监控进程
            self.monitor_terminal()
            
        except Exception as e:
            messagebox.showerror("Error", f"Launch failed: {e}")
            self.reset_ui()
            
    def monitor_terminal(self):
        """监控终端进程"""
        if self.terminal_process and self.terminal_process.poll() is None:
            # 继续监控
            self.root.after(1000, self.monitor_terminal)
        else:
            # 进程结束
            self.terminal_process = None
            self.start_button.config(text="🔄 Restart", state=tk.NORMAL, bg=self.colors['button'])
            self.status.config(text="Exited", fg=self.colors['muted'])
            
            # 重新绑定悬停效果
            self.start_button.bind("<Enter>", lambda e: self.start_button.config(bg=self.colors['button_hover']))
            self.start_button.bind("<Leave>", lambda e: self.start_button.config(bg=self.colors['button']))
            
    def reset_ui(self):
        """重置界面"""
        self.start_button.config(text="▶ Launch", state=tk.NORMAL, bg=self.colors['button'])
        self.status.config(text="Ready", fg=self.colors['muted'])
        
    def run(self):
        """运行"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """关闭时清理"""
        if self.terminal_process:
            self.terminal_process.terminate()
        self.root.destroy()

def main():
    """主函数"""
    try:
        launcher = UltraMinimalLauncher()
        launcher.run()
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
