#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flat Style Start.bat Launcher
扁平化风格的start.bat启动器 - 保持原始布局但使用现代flat设计
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import os
import sys
import time
from datetime import datetime

class FlatStartLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BIOS Terminal Launcher")
        self.root.geometry("500x300")
        self.root.resizable(False, False)
        
        # Flat设计配色方案
        self.colors = {
            'bg': '#2C3E50',           # 深蓝灰背景
            'card_bg': '#34495E',      # 卡片背景
            'text_primary': '#ECF0F1', # 主文字 - 浅灰白
            'text_secondary': '#BDC3C7', # 次要文字 - 灰色
            'accent': '#3498DB',       # 强调色 - 蓝色
            'success': '#2ECC71',      # 成功色 - 绿色
            'warning': '#F39C12',      # 警告色 - 橙色
            'danger': '#E74C3C',       # 危险色 - 红色
            'button_bg': '#3498DB',    # 按钮背景
            'button_hover': '#2980B9', # 按钮悬停
            'button_disabled': '#7F8C8D' # 按钮禁用
        }
        
        self.terminal_process = None
        self.is_running = False
        
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """设置窗口样式"""
        self.root.configure(bg=self.colors['bg'])
        
        # 居中显示
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 500) // 2
        y = (screen_height - 300) // 2
        self.root.geometry(f"500x300+{x}+{y}")
        
    def create_widgets(self):
        """创建界面组件 - 模拟start.bat的布局"""
        # 主容器
        main_container = tk.Frame(self.root, bg=self.colors['bg'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题区域 - 对应start.bat的第一行输出
        self.create_title_section(main_container)
        
        # 信息区域 - 对应start.bat的提示信息
        self.create_info_section(main_container)
        
        # 控制区域 - 对应start.bat的启动功能
        self.create_control_section(main_container)
        
        # 状态区域 - 对应start.bat的pause功能
        self.create_status_section(main_container)
        
    def create_title_section(self, parent):
        """创建标题区域"""
        title_frame = tk.Frame(parent, bg=self.colors['card_bg'], height=60)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        title_frame.pack_propagate(False)
        
        # 主标题 - 对应 "Starting Stable BIOS Terminal (No Flickering)..."
        title_label = tk.Label(title_frame, 
                              text="Starting Stable BIOS Terminal",
                              bg=self.colors['card_bg'], 
                              fg=self.colors['text_primary'],
                              font=('Segoe UI', 16, 'normal'))
        title_label.pack(expand=True)
        
    def create_info_section(self, parent):
        """创建信息区域"""
        info_frame = tk.Frame(parent, bg=self.colors['card_bg'])
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 信息文字 - 对应 "Make sure your terminal window is large enough (at least 80x15)"
        info_text = "Make sure your terminal is at least 80x15 characters"
        info_label = tk.Label(info_frame, 
                             text=info_text,
                             bg=self.colors['card_bg'], 
                             fg=self.colors['text_secondary'],
                             font=('Segoe UI', 10))
        info_label.pack(pady=15)
        
        # 当前时间显示
        self.time_label = tk.Label(info_frame, 
                                  text="",
                                  bg=self.colors['card_bg'], 
                                  fg=self.colors['accent'],
                                  font=('Segoe UI', 9))
        self.time_label.pack(pady=(0, 15))
        
        # 更新时间
        self.update_time()
        
    def create_control_section(self, parent):
        """创建控制区域"""
        control_frame = tk.Frame(parent, bg=self.colors['bg'])
        control_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 启动按钮 - 对应start.bat的主要功能
        self.start_button = self.create_flat_button(
            control_frame, 
            "Start BIOS Terminal", 
            self.start_terminal,
            self.colors['success']
        )
        self.start_button.pack(pady=5)
        
        # 停止按钮
        self.stop_button = self.create_flat_button(
            control_frame, 
            "Stop Terminal", 
            self.stop_terminal,
            self.colors['danger']
        )
        self.stop_button.pack(pady=5)
        self.stop_button.config(state=tk.DISABLED, bg=self.colors['button_disabled'])
        
    def create_status_section(self, parent):
        """创建状态区域"""
        status_frame = tk.Frame(parent, bg=self.colors['card_bg'])
        status_frame.pack(fill=tk.X)
        
        # 状态文字 - 对应start.bat的pause提示
        self.status_label = tk.Label(status_frame, 
                                    text="Ready to start - Click button to continue",
                                    bg=self.colors['card_bg'], 
                                    fg=self.colors['text_secondary'],
                                    font=('Segoe UI', 9))
        self.status_label.pack(pady=15)
        
    def create_flat_button(self, parent, text, command, bg_color):
        """创建扁平化按钮"""
        button = tk.Button(parent, 
                          text=text,
                          command=command,
                          bg=bg_color,
                          fg='white',
                          font=('Segoe UI', 11, 'normal'),
                          width=25,
                          height=2,
                          relief=tk.FLAT,
                          bd=0,
                          cursor='hand2')
        
        # 添加悬停效果
        def on_enter(e):
            if button['state'] != tk.DISABLED:
                # 计算悬停颜色（稍微变暗）
                hover_color = self.darken_color(bg_color)
                button.config(bg=hover_color)
                
        def on_leave(e):
            if button['state'] != tk.DISABLED:
                button.config(bg=bg_color)
            else:
                button.config(bg=self.colors['button_disabled'])
                
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        
        return button
        
    def darken_color(self, color):
        """将颜色变暗一点"""
        color_map = {
            self.colors['success']: '#27AE60',
            self.colors['danger']: '#C0392B',
            self.colors['button_bg']: self.colors['button_hover']
        }
        return color_map.get(color, color)
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def start_terminal(self):
        """启动BIOS终端 - 对应start.bat的python stable_bios_terminal.py"""
        try:
            if not os.path.exists("stable_bios_terminal.py"):
                messagebox.showerror("Error", "stable_bios_terminal.py not found!")
                return
                
            # 更新状态 - 模拟start.bat的启动过程
            self.status_label.config(text="Starting BIOS Terminal...", 
                                   fg=self.colors['warning'])
            self.root.update()
            
            # 启动终端程序
            self.terminal_process = subprocess.Popen([sys.executable, "stable_bios_terminal.py"])
            
            self.is_running = True
            
            # 更新界面状态
            self.start_button.config(state=tk.DISABLED, bg=self.colors['button_disabled'])
            self.stop_button.config(state=tk.NORMAL, bg=self.colors['danger'])
            self.status_label.config(text="BIOS Terminal is running - Use terminal window for control", 
                                   fg=self.colors['success'])
            
            # 启动监控
            self.monitor_process()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start terminal: {e}")
            self.status_label.config(text="Failed to start terminal", 
                                   fg=self.colors['danger'])
            
    def stop_terminal(self):
        """停止BIOS终端"""
        try:
            if self.terminal_process:
                self.terminal_process.terminate()
                self.terminal_process = None
                
            self.is_running = False
            
            # 更新界面状态
            self.start_button.config(state=tk.NORMAL, bg=self.colors['success'])
            self.stop_button.config(state=tk.DISABLED, bg=self.colors['button_disabled'])
            self.status_label.config(text="Terminal stopped - Ready to start again", 
                                   fg=self.colors['text_secondary'])
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop terminal: {e}")
            
    def monitor_process(self):
        """监控进程状态"""
        if self.is_running and self.terminal_process:
            if self.terminal_process.poll() is not None:
                # 进程已结束 - 对应start.bat的pause功能
                self.stop_terminal()
                self.status_label.config(text="BIOS Terminal exited - Press any key to continue...", 
                                       fg=self.colors['accent'])
                return
                
        # 每秒检查一次
        if self.is_running:
            self.root.after(1000, self.monitor_process)
            
    def run(self):
        """运行GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """程序关闭时清理"""
        if self.is_running:
            self.stop_terminal()
        self.root.destroy()

def main():
    """主函数"""
    try:
        launcher = FlatStartLauncher()
        launcher.run()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
