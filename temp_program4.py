
import time
import random
import datetime
import sys
import os

print("=== File Monitor (Program 4) ===")
print("Running in separate window")
print("Press Ctrl+C to exit")
print()

try:
    counter = 0
    file_types = [".txt", ".log", ".dat", ".tmp", ".bak"]
    operations = ["Created", "Modified", "Deleted", "Accessed"]

    while True:
        counter += 1
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # 模拟文件操作
        filename = f"file_{random.randint(1000, 9999)}{random.choice(file_types)}"
        operation = random.choice(operations)
        size = random.randint(1, 1000)

        print(f"[{timestamp}] File Event #{counter}")
        print(f"File: {filename}")
        print(f"Operation: {operation}")
        print(f"Size: {size} KB")
        print(f"Path: C:\\temp\\monitor\\{filename}")
        print("-" * 50)

        time.sleep(2)

except KeyboardInterrupt:
    print("\nFile Monitor stopped by user")
except Exception as e:
    print(f"Error: {e}")
