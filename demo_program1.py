#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例程序1 - 系统监控模拟
"""

import time
import random
import psutil
import datetime

def main():
    """模拟系统监控程序"""
    print("=== 系统监控程序启动 ===", flush=True)
    
    counter = 0
    while True:
        counter += 1
        
        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # 模拟一些随机数据
        network_speed = random.randint(50, 1000)
        disk_io = random.randint(10, 100)
        
        # 输出监控信息
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] 监控周期 #{counter}", flush=True)
        print(f"CPU使用率: {cpu_percent:.1f}%", flush=True)
        print(f"内存使用: {memory.percent:.1f}% ({memory.used // (1024**3):.1f}GB/{memory.total // (1024**3):.1f}GB)", flush=True)
        print(f"网络速度: {network_speed} KB/s", flush=True)
        print(f"磁盘IO: {disk_io} MB/s", flush=True)
        print("-" * 40, flush=True)
        
        time.sleep(2)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("程序被中断")
    except Exception as e:
        print(f"程序出错: {e}")
