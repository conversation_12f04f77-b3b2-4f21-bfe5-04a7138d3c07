
import time
import random
import datetime
import sys

print("=== Network Monitor (Program 3) ===")
print("Running in separate window")
print("Press Ctrl+C to exit")
print()

try:
    counter = 0
    while True:
        counter += 1
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # 模拟网络数据
        download_speed = random.randint(50, 1000)
        upload_speed = random.randint(10, 100)
        ping = random.randint(10, 50)
        packets_sent = counter * 10
        packets_received = packets_sent - random.randint(0, 2)

        print(f"[{timestamp}] Network Status #{counter}")
        print(f"Download: {download_speed} KB/s | Upload: {upload_speed} KB/s")
        print(f"Ping: {ping}ms | Packets: {packets_received}/{packets_sent}")
        print("-" * 50)

        time.sleep(3)

except KeyboardInterrupt:
    print("\nNetwork Monitor stopped by user")
except Exception as e:
    print(f"Error: {e}")
