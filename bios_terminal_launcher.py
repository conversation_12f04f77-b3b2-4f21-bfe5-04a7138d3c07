#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BIOS Terminal GUI Launcher
图形界面启动器，类似start.bat的功能
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import subprocess
import threading
import queue
import os
import sys
import time
from datetime import datetime

class BIOSTerminalLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BIOS Terminal Launcher")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置图标和样式
        self.setup_style()
        
        # 程序状态
        self.terminal_process = None
        self.is_running = False
        self.output_queue = queue.Queue()
        self.start_time = None
        self.last_activity = None
        
        # 创建界面
        self.create_widgets()
        
        # 启动输出监控线程
        self.start_output_monitor()

        # 启动状态更新
        self.update_status_display()
        
    def setup_style(self):
        """设置界面样式"""
        # 设置深色主题，模拟BIOS风格
        self.root.configure(bg='#000080')  # BIOS蓝色背景
        
        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('BIOS.TFrame', background='#000080')
        style.configure('BIOS.TLabel', background='#000080', foreground='white', font=('Consolas', 10))
        style.configure('BIOS.TButton', font=('Consolas', 10, 'bold'))
        style.configure('Title.TLabel', background='#000080', foreground='yellow', 
                       font=('Consolas', 16, 'bold'))
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, style='BIOS.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="BIOS TERMINAL LAUNCHER", 
                               style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # 信息显示区域
        info_frame = ttk.Frame(main_frame, style='BIOS.TFrame')
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(info_frame, text="System Information:", 
                 style='BIOS.TLabel').pack(anchor=tk.W)
        
        # 系统信息
        self.info_text = tk.Text(info_frame, height=6, width=80, 
                                bg='black', fg='cyan', font=('Consolas', 9),
                                state=tk.DISABLED)
        self.info_text.pack(fill=tk.X, pady=(5, 0))
        
        # 控制按钮区域
        control_frame = ttk.Frame(main_frame, style='BIOS.TFrame')
        control_frame.pack(fill=tk.X, pady=10)
        
        # 按钮
        self.start_button = ttk.Button(control_frame, text="Start BIOS Terminal", 
                                      command=self.start_terminal, style='BIOS.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="Stop Terminal", 
                                     command=self.stop_terminal, style='BIOS.TButton',
                                     state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.refresh_button = ttk.Button(control_frame, text="Refresh Info", 
                                        command=self.refresh_info, style='BIOS.TButton')
        self.refresh_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态显示
        status_frame = ttk.Frame(main_frame, style='BIOS.TFrame')
        status_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(status_frame, text="Terminal Status:",
                 style='BIOS.TLabel').pack(anchor=tk.W)

        # 状态信息网格
        status_grid = ttk.Frame(status_frame, style='BIOS.TFrame')
        status_grid.pack(fill=tk.X, pady=(5, 0))

        # 第一行状态
        ttk.Label(status_grid, text="Status:", style='BIOS.TLabel').grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.status_label = ttk.Label(status_grid, text="Ready to start", style='BIOS.TLabel')
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(status_grid, text="Runtime:", style='BIOS.TLabel').grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.runtime_label = ttk.Label(status_grid, text="00:00:00", style='BIOS.TLabel')
        self.runtime_label.grid(row=0, column=3, sticky=tk.W)

        # 第二行状态
        ttk.Label(status_grid, text="Last Activity:", style='BIOS.TLabel').grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.activity_label = ttk.Label(status_grid, text="None", style='BIOS.TLabel')
        self.activity_label.grid(row=1, column=1, columnspan=3, sticky=tk.W)
        
        # 输出日志区域
        log_frame = ttk.Frame(main_frame, style='BIOS.TFrame')
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        ttk.Label(log_frame, text="Output Log:", 
                 style='BIOS.TLabel').pack(anchor=tk.W)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, 
                                                 bg='black', fg='green', 
                                                 font=('Consolas', 9),
                                                 state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 底部状态栏
        self.status_bar = ttk.Label(main_frame, text="BIOS Terminal Launcher Ready", 
                                   style='BIOS.TLabel')
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))
        
        # 初始化显示系统信息
        self.refresh_info()
        
    def refresh_info(self):
        """刷新系统信息"""
        try:
            import platform
            import psutil
            
            info_lines = [
                f"System: {platform.system()} {platform.release()}",
                f"Python: {platform.python_version()}",
                f"Machine: {platform.machine()}",
                f"CPU Cores: {psutil.cpu_count()}",
                f"Memory: {psutil.virtual_memory().total // (1024**3)} GB",
                f"Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ]
            
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, "\n".join(info_lines))
            self.info_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.log_message(f"Error refreshing info: {e}")
            
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
    def start_terminal(self):
        """启动BIOS终端程序"""
        if self.is_running:
            messagebox.showwarning("Warning", "Terminal is already running!")
            return
            
        try:
            # 检查文件是否存在
            if not os.path.exists("stable_bios_terminal.py"):
                messagebox.showerror("Error", "stable_bios_terminal.py not found!")
                return
                
            self.log_message("Starting BIOS Terminal...")
            self.status_label.config(text="Starting...")
            
            # 启动终端程序
            self.terminal_process = subprocess.Popen(
                [sys.executable, "stable_bios_terminal.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.is_running = True
            self.start_time = datetime.now()
            self.last_activity = datetime.now()
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.status_label.config(text="Running")
            
            # 启动输出读取线程
            threading.Thread(target=self.read_terminal_output, daemon=True).start()
            
            self.log_message("BIOS Terminal started successfully")
            self.status_bar.config(text="BIOS Terminal is running...")
            
        except Exception as e:
            self.log_message(f"Failed to start terminal: {e}")
            messagebox.showerror("Error", f"Failed to start terminal: {e}")
            
    def stop_terminal(self):
        """停止BIOS终端程序"""
        if not self.is_running:
            return
            
        try:
            self.log_message("Stopping BIOS Terminal...")
            
            if self.terminal_process:
                self.terminal_process.terminate()
                self.terminal_process.wait(timeout=5)
                
            self.is_running = False
            self.start_time = None
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.status_label.config(text="Stopped")
            self.runtime_label.config(text="00:00:00")
            self.activity_label.config(text="Terminal stopped")
            
            self.log_message("BIOS Terminal stopped")
            self.status_bar.config(text="BIOS Terminal Launcher Ready")
            
        except Exception as e:
            self.log_message(f"Error stopping terminal: {e}")
            
    def read_terminal_output(self):
        """读取终端程序输出"""
        try:
            while self.is_running and self.terminal_process:
                line = self.terminal_process.stdout.readline()
                if line:
                    self.output_queue.put(line.strip())
                elif self.terminal_process.poll() is not None:
                    break
                    
        except Exception as e:
            self.output_queue.put(f"Output reading error: {e}")
            
    def start_output_monitor(self):
        """启动输出监控"""
        def monitor():
            try:
                while True:
                    try:
                        message = self.output_queue.get(timeout=0.1)
                        if message:
                            self.log_message(f"Terminal: {message}")
                            self.last_activity = datetime.now()
                            self.activity_label.config(text=f"Last output: {self.last_activity.strftime('%H:%M:%S')}")
                    except queue.Empty:
                        pass
                    except Exception as e:
                        print(f"Monitor error: {e}")
                        
            except Exception as e:
                print(f"Monitor thread error: {e}")
                
        threading.Thread(target=monitor, daemon=True).start()

    def update_status_display(self):
        """更新状态显示"""
        try:
            if self.is_running and self.start_time:
                # 计算运行时间
                runtime = datetime.now() - self.start_time
                hours, remainder = divmod(int(runtime.total_seconds()), 3600)
                minutes, seconds = divmod(remainder, 60)
                runtime_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                self.runtime_label.config(text=runtime_str)

                # 检查进程状态
                if self.terminal_process and self.terminal_process.poll() is not None:
                    # 进程已结束
                    self.log_message("Terminal process ended unexpectedly")
                    self.stop_terminal()

        except Exception as e:
            print(f"Status update error: {e}")

        # 每秒更新一次
        self.root.after(1000, self.update_status_display)

    def on_closing(self):
        """程序关闭时的清理"""
        if self.is_running:
            self.stop_terminal()
        self.root.destroy()
        
    def run(self):
        """运行GUI程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """主函数"""
    try:
        # 检查依赖
        required_modules = ['psutil']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
                
        if missing_modules:
            print(f"Missing required modules: {', '.join(missing_modules)}")
            print("Please install them using: pip install " + " ".join(missing_modules))
            return
            
        # 启动GUI
        launcher = BIOSTerminalLauncher()
        launcher.run()
        
    except Exception as e:
        print(f"Error starting launcher: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
