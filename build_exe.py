#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build script for creating BIOS Terminal Launcher executable
使用PyInstaller将GUI启动器打包成exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """检查必要的依赖"""
    required_packages = ['pyinstaller', 'psutil']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Missing required packages: {', '.join(missing_packages)}")
        print(f"Please install them using: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['bios_terminal_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('stable_bios_terminal.py', '.'),
        ('demo_program1.py', '.'),
        ('demo_program2.py', '.'),
        ('requirements.txt', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=['psutil'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='BIOS_Terminal_Launcher',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open('launcher.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("Created launcher.spec file")

def build_executable():
    """构建可执行文件"""
    try:
        print("Building executable with PyInstaller...")
        
        # 运行PyInstaller
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'launcher.spec']
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("Build successful!")
            print("Executable created in: dist/BIOS_Terminal_Launcher.exe")
            return True
        else:
            print("Build failed!")
            print("Error output:", result.stderr)
            return False
            
    except Exception as e:
        print(f"Build error: {e}")
        return False

def create_portable_package():
    """创建便携式包"""
    try:
        # 创建发布目录
        release_dir = Path("release")
        if release_dir.exists():
            shutil.rmtree(release_dir)
        release_dir.mkdir()
        
        # 复制exe文件
        exe_path = Path("dist/BIOS_Terminal_Launcher.exe")
        if exe_path.exists():
            shutil.copy2(exe_path, release_dir / "BIOS_Terminal_Launcher.exe")
        
        # 复制必要文件
        files_to_copy = [
            "stable_bios_terminal.py",
            "demo_program1.py", 
            "demo_program2.py",
            "requirements.txt",
            "README.md",
            "start.bat",
            "start.sh"
        ]
        
        for file_name in files_to_copy:
            file_path = Path(file_name)
            if file_path.exists():
                shutil.copy2(file_path, release_dir / file_name)
        
        # 创建使用说明
        readme_content = """# BIOS Terminal Launcher - Portable Package

## 文件说明

### 可执行文件
- `BIOS_Terminal_Launcher.exe` - GUI启动器（推荐使用）
- `start.bat` - 命令行启动脚本（Windows）
- `start.sh` - 命令行启动脚本（Linux/macOS）

### 核心程序文件
- `stable_bios_terminal.py` - 主BIOS终端程序
- `demo_program1.py` - 示例程序1（系统监控）
- `demo_program2.py` - 示例程序2（日志生成器）

### 文档
- `README.md` - 详细使用说明
- `requirements.txt` - Python依赖列表

## 使用方法

### 方法1：使用GUI启动器（推荐）
1. 双击 `BIOS_Terminal_Launcher.exe`
2. 在GUI界面中点击 "Start BIOS Terminal" 按钮
3. 享受BIOS风格的终端体验！

### 方法2：使用命令行
1. 双击 `start.bat`（Windows）或运行 `./start.sh`（Linux/macOS）
2. 直接启动BIOS终端程序

## 系统要求
- Windows 10/11（推荐）
- 终端大小至少 80x15 字符
- 如果使用Python版本，需要安装依赖：`pip install -r requirements.txt`

## 功能特点
- 🎮 BIOS风格界面设计
- 📊 实时系统监控
- 🔄 多程序管理
- 📝 日志输出显示
- 🖥️ 图形化启动器

## 故障排除
如果遇到问题，请：
1. 确保终端窗口足够大
2. 检查是否有杀毒软件阻止运行
3. 尝试以管理员身份运行

祝您使用愉快！
"""
        
        with open(release_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"Portable package created in: {release_dir}")
        print("Contents:")
        for item in release_dir.iterdir():
            print(f"  - {item.name}")
        
        return True
        
    except Exception as e:
        print(f"Error creating portable package: {e}")
        return False

def main():
    """主函数"""
    print("BIOS Terminal Launcher - Build Script")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查必要文件
    required_files = ['bios_terminal_launcher.py', 'stable_bios_terminal.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"Missing required files: {', '.join(missing_files)}")
        return
    
    print("All dependencies and files found!")
    print()
    
    # 创建spec文件
    create_spec_file()
    
    # 构建可执行文件
    if build_executable():
        print()
        print("Creating portable package...")
        if create_portable_package():
            print()
            print("✅ Build completed successfully!")
            print("📦 Portable package ready in 'release' folder")
            print("🚀 You can now distribute the 'release' folder")
        else:
            print("❌ Failed to create portable package")
    else:
        print("❌ Build failed")

if __name__ == "__main__":
    main()
