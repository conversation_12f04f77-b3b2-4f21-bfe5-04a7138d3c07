#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Winamp Style BIOS Terminal Launcher
经典Winamp风格的BIOS终端启动器
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import os
import sys
import time
import threading
from datetime import datetime

class WinampStyleLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BIOS Terminal Launcher v2.0")
        self.root.geometry("480x320")
        self.root.resizable(False, False)
        
        # 经典Winamp配色
        self.colors = {
            'bg_main': '#0A0A0A',           # 主背景 - 深黑
            'bg_panel': '#1A1A1A',         # 面板背景 - 深灰
            'bg_button': '#2A2A2A',        # 按钮背景
            'bg_active': '#00FF00',        # 激活状态 - 亮绿
            'text_main': '#00FF00',        # 主文字 - 绿色
            'text_secondary': '#FFFF00',   # 次要文字 - 黄色
            'text_info': '#00FFFF',        # 信息文字 - 青色
            'border': '#333333',           # 边框颜色
            'led_on': '#FF0000',           # LED开启 - 红色
            'led_off': '#330000'           # LED关闭 - 暗红
        }
        
        self.terminal_process = None
        self.is_running = False
        self.start_time = None
        
        self.setup_window()
        self.create_widgets()
        self.start_animations()
        
    def setup_window(self):
        """设置窗口样式"""
        self.root.configure(bg=self.colors['bg_main'])
        
        # 移除标题栏装饰，创建自定义标题栏
        self.root.overrideredirect(True)
        
        # 居中显示
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 480) // 2
        y = (screen_height - 320) // 2
        self.root.geometry(f"480x320+{x}+{y}")
        
        # 添加边框效果
        self.root.configure(highlightbackground=self.colors['border'], 
                           highlightthickness=2)
        
    def create_widgets(self):
        """创建Winamp风格的界面组件"""
        # 自定义标题栏
        self.create_title_bar()
        
        # 主显示区域
        self.create_main_display()
        
        # 控制按钮区域
        self.create_control_panel()
        
        # 状态指示器
        self.create_status_indicators()
        
        # 底部信息栏
        self.create_info_bar()
        
    def create_title_bar(self):
        """创建自定义标题栏"""
        title_frame = tk.Frame(self.root, bg=self.colors['bg_panel'], height=25)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        # 标题文字
        title_label = tk.Label(title_frame, text="BIOS TERMINAL LAUNCHER v2.0", 
                              bg=self.colors['bg_panel'], fg=self.colors['text_secondary'],
                              font=('Courier New', 8, 'bold'))
        title_label.pack(side=tk.LEFT, padx=5, pady=3)
        
        # 关闭按钮
        close_btn = tk.Button(title_frame, text="×", 
                             bg=self.colors['bg_button'], fg=self.colors['text_main'],
                             font=('Courier New', 10, 'bold'),
                             width=3, height=1,
                             command=self.close_app,
                             relief=tk.FLAT)
        close_btn.pack(side=tk.RIGHT, padx=2, pady=2)
        
        # 最小化按钮
        min_btn = tk.Button(title_frame, text="−", 
                           bg=self.colors['bg_button'], fg=self.colors['text_main'],
                           font=('Courier New', 10, 'bold'),
                           width=3, height=1,
                           command=self.minimize_app,
                           relief=tk.FLAT)
        min_btn.pack(side=tk.RIGHT, padx=2, pady=2)
        
        # 使标题栏可拖拽
        title_frame.bind("<Button-1>", self.start_drag)
        title_frame.bind("<B1-Motion>", self.on_drag)
        title_label.bind("<Button-1>", self.start_drag)
        title_label.bind("<B1-Motion>", self.on_drag)
        
    def create_main_display(self):
        """创建主显示区域"""
        display_frame = tk.Frame(self.root, bg=self.colors['bg_main'])
        display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # LCD风格显示屏
        lcd_frame = tk.Frame(display_frame, bg=self.colors['bg_panel'], 
                            relief=tk.SUNKEN, bd=2)
        lcd_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 主状态显示
        self.main_display = tk.Label(lcd_frame, 
                                    text="BIOS TERMINAL READY", 
                                    bg='#000000', fg=self.colors['text_main'],
                                    font=('Courier New', 12, 'bold'),
                                    height=2)
        self.main_display.pack(fill=tk.X, padx=2, pady=2)
        
        # 滚动信息显示
        self.info_display = tk.Label(lcd_frame, 
                                    text="Make sure your terminal is at least 80x15 characters", 
                                    bg='#000000', fg=self.colors['text_info'],
                                    font=('Courier New', 8),
                                    height=1)
        self.info_display.pack(fill=tk.X, padx=2, pady=(0, 2))
        
    def create_control_panel(self):
        """创建控制面板"""
        control_frame = tk.Frame(self.root, bg=self.colors['bg_main'])
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 左侧按钮组
        left_buttons = tk.Frame(control_frame, bg=self.colors['bg_main'])
        left_buttons.pack(side=tk.LEFT, fill=tk.Y)
        
        # 主要控制按钮
        self.start_btn = self.create_winamp_button(left_buttons, "▶ START", 
                                                  self.start_terminal, 
                                                  self.colors['bg_active'])
        self.start_btn.pack(pady=2)
        
        self.stop_btn = self.create_winamp_button(left_buttons, "⏹ STOP", 
                                                 self.stop_terminal, 
                                                 self.colors['led_on'])
        self.stop_btn.pack(pady=2)
        self.stop_btn.config(state=tk.DISABLED)
        
        # 右侧状态区域
        right_panel = tk.Frame(control_frame, bg=self.colors['bg_main'])
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # 时间显示
        time_frame = tk.Frame(right_panel, bg=self.colors['bg_panel'], 
                             relief=tk.SUNKEN, bd=1)
        time_frame.pack(fill=tk.X, pady=2)
        
        self.time_display = tk.Label(time_frame, text="00:00:00", 
                                    bg='#000000', fg=self.colors['text_secondary'],
                                    font=('Courier New', 10, 'bold'))
        self.time_display.pack(pady=2)
        
        # 运行时间显示
        runtime_frame = tk.Frame(right_panel, bg=self.colors['bg_panel'], 
                                relief=tk.SUNKEN, bd=1)
        runtime_frame.pack(fill=tk.X, pady=2)
        
        tk.Label(runtime_frame, text="RUNTIME", 
                bg=self.colors['bg_panel'], fg=self.colors['text_info'],
                font=('Courier New', 7)).pack()
        
        self.runtime_display = tk.Label(runtime_frame, text="00:00:00", 
                                       bg='#000000', fg=self.colors['text_main'],
                                       font=('Courier New', 9, 'bold'))
        self.runtime_display.pack(pady=(0, 2))
        
    def create_status_indicators(self):
        """创建状态指示器"""
        status_frame = tk.Frame(self.root, bg=self.colors['bg_main'])
        status_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # LED指示器
        led_frame = tk.Frame(status_frame, bg=self.colors['bg_panel'], 
                            relief=tk.SUNKEN, bd=1)
        led_frame.pack(side=tk.LEFT, padx=(0, 5))
        
        tk.Label(led_frame, text="STATUS", 
                bg=self.colors['bg_panel'], fg=self.colors['text_info'],
                font=('Courier New', 7)).pack()
        
        led_container = tk.Frame(led_frame, bg=self.colors['bg_panel'])
        led_container.pack(pady=2)
        
        # 电源LED
        self.power_led = tk.Label(led_container, text="●", 
                                 bg=self.colors['bg_panel'], fg=self.colors['led_off'],
                                 font=('Courier New', 12))
        self.power_led.pack(side=tk.LEFT, padx=2)
        
        tk.Label(led_container, text="PWR", 
                bg=self.colors['bg_panel'], fg=self.colors['text_info'],
                font=('Courier New', 6)).pack(side=tk.LEFT, padx=(0, 5))
        
        # 活动LED
        self.activity_led = tk.Label(led_container, text="●", 
                                    bg=self.colors['bg_panel'], fg=self.colors['led_off'],
                                    font=('Courier New', 12))
        self.activity_led.pack(side=tk.LEFT, padx=2)
        
        tk.Label(led_container, text="ACT", 
                bg=self.colors['bg_panel'], fg=self.colors['text_info'],
                font=('Courier New', 6)).pack(side=tk.LEFT)
        
        # 频谱分析器风格的装饰
        spectrum_frame = tk.Frame(status_frame, bg=self.colors['bg_panel'], 
                                 relief=tk.SUNKEN, bd=1)
        spectrum_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0))
        
        tk.Label(spectrum_frame, text="SPECTRUM", 
                bg=self.colors['bg_panel'], fg=self.colors['text_info'],
                font=('Courier New', 7)).pack()
        
        self.spectrum_bars = []
        bars_container = tk.Frame(spectrum_frame, bg='#000000')
        bars_container.pack(fill=tk.X, padx=2, pady=2)
        
        for i in range(10):
            bar = tk.Label(bars_container, text="█", 
                          bg='#000000', fg=self.colors['led_off'],
                          font=('Courier New', 8))
            bar.pack(side=tk.LEFT, padx=1)
            self.spectrum_bars.append(bar)
            
    def create_info_bar(self):
        """创建底部信息栏"""
        info_frame = tk.Frame(self.root, bg=self.colors['bg_panel'], height=20)
        info_frame.pack(fill=tk.X, side=tk.BOTTOM)
        info_frame.pack_propagate(False)
        
        self.status_text = tk.Label(info_frame, text="Ready to start BIOS Terminal", 
                                   bg=self.colors['bg_panel'], fg=self.colors['text_info'],
                                   font=('Courier New', 7))
        self.status_text.pack(side=tk.LEFT, padx=5, pady=2)
        
        # 版本信息
        version_label = tk.Label(info_frame, text="v2.0", 
                                bg=self.colors['bg_panel'], fg=self.colors['text_secondary'],
                                font=('Courier New', 7))
        version_label.pack(side=tk.RIGHT, padx=5, pady=2)
        
    def create_winamp_button(self, parent, text, command, active_color):
        """创建Winamp风格的按钮"""
        btn = tk.Button(parent, text=text, 
                       bg=self.colors['bg_button'], fg=self.colors['text_main'],
                       font=('Courier New', 9, 'bold'),
                       width=12, height=1,
                       command=command,
                       relief=tk.RAISED, bd=2,
                       activebackground=active_color,
                       activeforeground='#000000')
        
        # 添加悬停效果
        def on_enter(e):
            if btn['state'] != tk.DISABLED:
                btn.config(bg=active_color, fg='#000000')
                
        def on_leave(e):
            if btn['state'] != tk.DISABLED:
                btn.config(bg=self.colors['bg_button'], fg=self.colors['text_main'])
                
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        
        return btn
        
    def start_terminal(self):
        """启动BIOS终端"""
        try:
            if not os.path.exists("stable_bios_terminal.py"):
                messagebox.showerror("Error", "stable_bios_terminal.py not found!")
                return
                
            self.main_display.config(text="STARTING BIOS TERMINAL...")
            self.status_text.config(text="Initializing terminal process...")
            self.root.update()
            
            # 启动终端程序
            self.terminal_process = subprocess.Popen([sys.executable, "stable_bios_terminal.py"])
            
            self.is_running = True
            self.start_time = datetime.now()
            
            # 更新界面状态
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            self.power_led.config(fg=self.colors['led_on'])
            self.main_display.config(text="BIOS TERMINAL RUNNING")
            self.status_text.config(text="Terminal is running - Use terminal window for control")
            
            # 启动监控
            self.monitor_process()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start terminal: {e}")
            self.main_display.config(text="STARTUP FAILED")
            self.status_text.config(text="Failed to start terminal")
            
    def stop_terminal(self):
        """停止BIOS终端"""
        try:
            if self.terminal_process:
                self.terminal_process.terminate()
                self.terminal_process = None
                
            self.is_running = False
            self.start_time = None
            
            # 更新界面状态
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.power_led.config(fg=self.colors['led_off'])
            self.activity_led.config(fg=self.colors['led_off'])
            self.main_display.config(text="TERMINAL STOPPED")
            self.status_text.config(text="Terminal stopped by user")
            self.runtime_display.config(text="00:00:00")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop terminal: {e}")
            
    def monitor_process(self):
        """监控进程状态"""
        if self.is_running and self.terminal_process:
            if self.terminal_process.poll() is not None:
                # 进程已结束
                self.stop_terminal()
                self.main_display.config(text="TERMINAL EXITED")
                self.status_text.config(text="Terminal process ended")
                return
                
        # 每秒检查一次
        if self.is_running:
            self.root.after(1000, self.monitor_process)
            
    def start_animations(self):
        """启动动画效果"""
        self.update_time()
        self.update_runtime()
        self.animate_spectrum()
        self.animate_activity_led()
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_display.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def update_runtime(self):
        """更新运行时间"""
        if self.is_running and self.start_time:
            runtime = datetime.now() - self.start_time
            hours, remainder = divmod(int(runtime.total_seconds()), 3600)
            minutes, seconds = divmod(remainder, 60)
            runtime_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            self.runtime_display.config(text=runtime_str)
            
        self.root.after(1000, self.update_runtime)
        
    def animate_spectrum(self):
        """动画频谱显示"""
        if self.is_running:
            import random
            for bar in self.spectrum_bars:
                if random.random() > 0.7:
                    bar.config(fg=self.colors['bg_active'])
                else:
                    bar.config(fg=self.colors['led_off'])
        else:
            for bar in self.spectrum_bars:
                bar.config(fg=self.colors['led_off'])
                
        self.root.after(200, self.animate_spectrum)
        
    def animate_activity_led(self):
        """动画活动LED"""
        if self.is_running:
            current_color = self.activity_led.cget('fg')
            if current_color == self.colors['led_off']:
                self.activity_led.config(fg=self.colors['bg_active'])
            else:
                self.activity_led.config(fg=self.colors['led_off'])
        else:
            self.activity_led.config(fg=self.colors['led_off'])
            
        self.root.after(500, self.animate_activity_led)
        
    # 窗口拖拽功能
    def start_drag(self, event):
        self.drag_start_x = event.x
        self.drag_start_y = event.y
        
    def on_drag(self, event):
        x = self.root.winfo_x() + event.x - self.drag_start_x
        y = self.root.winfo_y() + event.y - self.drag_start_y
        self.root.geometry(f"+{x}+{y}")
        
    def minimize_app(self):
        self.root.iconify()
        
    def close_app(self):
        if self.is_running:
            self.stop_terminal()
        self.root.destroy()
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        launcher = WinampStyleLauncher()
        launcher.run()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
