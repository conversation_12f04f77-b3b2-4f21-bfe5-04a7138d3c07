#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳定版BIOS风格Windows终端程序 - 修复闪烁问题
"""

import curses
import os
import queue
import signal
import subprocess
import sys
import threading
import time


class StableBIOSTerminal:
    def __init__(self):
        self.stdscr = None
        self.menu_win = None
        self.overall_win = None  # 新增：OVERALL信息窗口
        self.output1_win = None
        self.output2_win = None
        self.status_win = None
        
        # 菜单选项
        self.menu_items = [
            "Start Program 1",
            "Start Program 2",
            "Stop Program 1",
            "Stop Program 2",
            "Clear Output 1",
            "Clear Output 2",
            "Start Program 3 (New Window)",
            "Start Program 4 (New Window)",
            "Stop Program 3",
            "Stop Program 4",
            "System Info",
            "Exit"
        ]
        self.current_menu = 0
        
        # 子程序管理
        self.processes = {
            'program1': None,
            'program2': None,
            'program3': None,  # 新窗口程序
            'program4': None   # 新窗口程序
        }

        # 输出缓冲区
        self.output_buffers = {
            'program0': [],  # 新增：OVERALL信息缓冲区
            'program1': [],
            'program2': []
        }

        # 输出队列
        self.output_queues = {
            'program0': queue.Queue(),  # 新增：OVERALL信息队列
            'program1': queue.Queue(),
            'program2': queue.Queue()
        }
        
        self.running = True
        self.need_redraw = True  # 控制重绘
        self.last_terminal_size = (0, 0)  # 跟踪终端大小变化
        self.resize_flag = False  # 终端大小改变标志

    def handle_resize(self, signum, frame):
        """处理终端大小改变信号"""
        self.resize_flag = True

    def init_colors(self):
        """初始化颜色方案"""
        try:
            curses.start_color()
            curses.init_pair(1, curses.COLOR_WHITE, curses.COLOR_BLUE)    # 标题栏
            curses.init_pair(2, curses.COLOR_BLACK, curses.COLOR_WHITE)   # 菜单选中
            curses.init_pair(3, curses.COLOR_WHITE, curses.COLOR_BLACK)   # 普通文本
            curses.init_pair(4, curses.COLOR_GREEN, curses.COLOR_BLACK)   # 程序输出
            curses.init_pair(5, curses.COLOR_RED, curses.COLOR_BLACK)     # 错误信息
            curses.init_pair(6, curses.COLOR_CYAN, curses.COLOR_BLACK)    # 状态信息
        except:
            pass
        
    def setup_windows(self):
        """设置窗口布局"""
        height, width = self.stdscr.getmaxyx()

        # 确保终端足够大
        if height < 16 or width < 80:
            raise Exception(f"Terminal too small. Need at least 80x16, got {width}x{height}")

        # 删除旧窗口（如果存在）
        if hasattr(self, 'menu_win') and self.menu_win:
            del self.menu_win
        if hasattr(self, 'overall_win') and self.overall_win:
            del self.overall_win
        if hasattr(self, 'output1_win') and self.output1_win:
            del self.output1_win
        if hasattr(self, 'output2_win') and self.output2_win:
            del self.output2_win
        if hasattr(self, 'status_win') and self.status_win:
            del self.status_win

        # 计算布局
        menu_width = min(40, width // 2.5)  # 增加菜单宽度
        right_width = width - menu_width - 1

        # 计算可用空间 (标题栏1行 + 状态栏1行 = 2行被占用)
        available_height = height - 3  # 从y=1到y=height-2的可用高度，为状态栏留出空间

        # OVERALL信息窗口 (横跨整个右侧，高度约1/4)
        overall_height = max(4, available_height // 4)
        self.overall_win = curses.newwin(overall_height, right_width, 1, menu_width + 1)

        # 计算右侧剩余空间
        remaining_height = available_height - overall_height
        output_height = remaining_height // 2
        output1_y = 1 + overall_height
        output2_height = remaining_height - output_height
        output2_y = output1_y + output_height

        # 左侧菜单窗口 (从y=1到y=height-2，与右侧窗口底部对齐)
        self.menu_win = curses.newwin(available_height, menu_width, 1, 0)

        # 右侧输出窗口1 (OVERALL窗口下方的上半部分)
        self.output1_win = curses.newwin(output_height, right_width, output1_y, menu_width + 1)

        # 右侧输出窗口2 (下半部分，确保底部对齐到y=height-2)
        if output2_height > 0:
            self.output2_win = curses.newwin(output2_height, right_width, output2_y, menu_width + 1)
        else:
            # 如果空间不够，创建最小窗口
            self.output2_win = curses.newwin(1, right_width, height - 2, menu_width + 1)

        # 状态栏
        self.status_win = curses.newwin(1, width, height - 1, 0)

        # 记录当前终端大小
        self.last_terminal_size = (height, width)

    def check_terminal_resize(self):
        """检查终端是否被调整大小"""
        try:
            # 检查信号标志或实际大小变化
            height, width = self.stdscr.getmaxyx()
            if self.resize_flag or (height, width) != self.last_terminal_size:
                # 重置信号标志
                self.resize_flag = False

                # 完全重新初始化curses
                curses.endwin()
                self.stdscr = curses.initscr()
                curses.noecho()
                curses.cbreak()
                self.stdscr.keypad(True)
                curses.curs_set(0)
                self.stdscr.timeout(200)

                # 重新初始化颜色
                self.init_colors()

                # 重新设置窗口
                self.setup_windows()

                # 强制重绘所有内容
                self.force_full_redraw()

                return True
            return False
        except:
            return False

    def force_full_redraw(self):
        """强制完全重绘所有界面元素"""
        try:
            # 清除整个屏幕
            self.stdscr.clear()
            self.stdscr.refresh()

            # 重绘标题
            self.draw_title()

            # 重绘菜单（强制）
            self.menu_win.clear()
            self.menu_win.box()
            self.safe_addstr(self.menu_win, 1, 2, "CONTROL MENU", curses.color_pair(1))

            for i, item in enumerate(self.menu_items):
                y = i + 3
                max_y, max_x = self.menu_win.getmaxyx()
                if y >= max_y - 1:
                    break

                if i == self.current_menu:
                    self.safe_addstr(self.menu_win, y, 1, f" {item} ", curses.color_pair(2))
                else:
                    self.safe_addstr(self.menu_win, y, 2, f" {item}", curses.color_pair(3))

            self.menu_win.refresh()

            # 重绘OVERALL窗口
            self.draw_output_window(self.overall_win, "OVERALL INFO", self.output_buffers['program0'])

            # 重绘输出窗口
            self.draw_output_window(self.output1_win, "PROGRAM 1 OUTPUT", self.output_buffers['program1'])
            self.draw_output_window(self.output2_win, "PROGRAM 2 OUTPUT", self.output_buffers['program2'])

            # 重绘状态栏
            self.draw_status()

            # 最终刷新
            self.stdscr.refresh()

        except Exception as e:
            # 如果重绘失败，记录错误
            try:
                self.output_queues['program0'].put(f"Redraw error: {e}")
            except:
                pass

    def safe_addstr(self, win, y, x, text, attr=None):
        """安全地添加字符串，避免越界"""
        try:
            max_y, max_x = win.getmaxyx()
            if y >= max_y or x >= max_x:
                return
            
            # 截断文本以适应窗口
            available_width = max_x - x - 1
            if available_width > 0:
                text = text[:available_width]
                if attr:
                    win.attron(attr)
                win.addstr(y, x, text)
                if attr:
                    win.attroff(attr)
        except curses.error:
            pass
        
    def draw_title(self):
        """绘制标题栏"""
        height, width = self.stdscr.getmaxyx()
        title = " BIOS TERMINAL - PYTHON VERSION "
        try:
            # 填充整行蓝色背景
            self.stdscr.attron(curses.color_pair(1))
            self.stdscr.addstr(0, 0, " " * width)
            # 居中显示标题
            title_x = max(0, (width - len(title)) // 2)
            self.stdscr.addstr(0, title_x, title[:width-1])
            self.stdscr.attroff(curses.color_pair(1))
        except curses.error:
            pass
        
    def draw_menu(self):
        """绘制左侧菜单"""
        try:
            self.menu_win.clear()
            self.menu_win.box()
            
            # 菜单标题
            self.safe_addstr(self.menu_win, 1, 2, "CONTROL MENU", curses.color_pair(1))
            
            # 菜单项
            for i, item in enumerate(self.menu_items):
                y = i + 3
                max_y, max_x = self.menu_win.getmaxyx()
                if y >= max_y - 1:
                    break
                    
                if i == self.current_menu:
                    # 选中项使用反色显示
                    self.safe_addstr(self.menu_win, y, 1, f" {item} ", curses.color_pair(2))
                else:
                    self.safe_addstr(self.menu_win, y, 2, f" {item}", curses.color_pair(3))
            
            self.menu_win.refresh()
        except Exception:
            pass
        
    def draw_output_window(self, win, title, buffer):
        """绘制单个输出窗口"""
        try:
            win.clear()
            win.box()
            self.safe_addstr(win, 0, 2, f" {title} ", curses.color_pair(1))
            
            # 显示输出
            max_y, max_x = win.getmaxyx()
            max_lines = max(0, max_y - 3)
            start_line = max(0, len(buffer) - max_lines)
            
            for i, line in enumerate(buffer[start_line:]):
                if i < max_lines:
                    self.safe_addstr(win, i + 2, 2, line, curses.color_pair(4))
            
            win.refresh()
        except Exception:
            pass
        
    def draw_status(self):
        """绘制状态栏"""
        try:
            self.status_win.clear()

            # 程序运行状态
            prog1_status = "RUN" if self.processes['program1'] and self.processes['program1'].poll() is None else "STOP"
            prog2_status = "RUN" if self.processes['program2'] and self.processes['program2'].poll() is None else "STOP"
            prog3_status = "RUN" if self.processes['program3'] and self.processes['program3'].poll() is None else "STOP"
            prog4_status = "RUN" if self.processes['program4'] and self.processes['program4'].poll() is None else "STOP"

            # 获取最新的OVERALL信息（如果有的话）
            latest_overall_msg = ""
            if self.output_buffers['program0']:
                # 获取最后一条消息，去掉时间戳
                last_msg = self.output_buffers['program0'][-1]
                if "] " in last_msg:
                    latest_overall_msg = last_msg.split("] ", 1)[1]
                else:
                    latest_overall_msg = last_msg

            # 构建状态栏文本：操作提示 | 程序状态 | 最新消息
            controls = "UP/DOWN:Select ENTER:Execute R:Refresh ESC:Exit"
            programs = f"P1:{prog1_status} P2:{prog2_status} P3:{prog3_status} P4:{prog4_status}"

            max_y, max_x = self.status_win.getmaxyx()

            if latest_overall_msg:
                # 如果有OVERALL消息，显示：控制 | 程序状态 | 消息
                status_text = f" {controls} | {programs} | {latest_overall_msg} "
            else:
                # 如果没有消息，只显示：控制 | 程序状态
                status_text = f" {controls} | {programs} "

            # 截断文本以适应窗口宽度
            if len(status_text) > max_x:
                status_text = status_text[:max_x - 3] + "..."

            # 传统BIOS风格：状态栏没有边框，直接显示文本
            # 填充背景色（如果支持颜色）
            try:
                self.status_win.attron(curses.color_pair(6))
                self.status_win.addstr(0, 0, " " * max_x)
                self.safe_addstr(self.status_win, 0, 0, status_text, curses.color_pair(6))
                self.status_win.attroff(curses.color_pair(6))
            except:
                # 如果颜色失败，使用普通文本
                self.status_win.addstr(0, 0, " " * max_x)
                self.safe_addstr(self.status_win, 0, 0, status_text)

            self.status_win.refresh()
        except Exception as e:
            # 如果状态栏绘制失败，显示简单信息
            try:
                self.status_win.clear()
                self.status_win.addstr(0, 0, " BIOS Terminal Ready ")
                self.status_win.refresh()
            except:
                pass
        
    def update_outputs(self):
        """更新输出显示"""
        updated = False

        # 处理程序0的输出队列 (OVERALL信息)
        try:
            while True:
                line = self.output_queues['program0'].get_nowait()
                timestamp = time.strftime("%H:%M:%S")
                formatted_line = f"[{timestamp}] {line}"
                self.output_buffers['program0'].append(formatted_line)

                # 限制缓冲区大小
                if len(self.output_buffers['program0']) > 100:
                    self.output_buffers['program0'] = self.output_buffers['program0'][-50:]

                updated = True
        except queue.Empty:
            pass

        # 处理程序1的输出队列
        try:
            while True:
                line = self.output_queues['program1'].get_nowait()
                timestamp = time.strftime("%H:%M:%S")
                formatted_line = f"[{timestamp}] {line}"
                self.output_buffers['program1'].append(formatted_line)

                # 限制缓冲区大小
                if len(self.output_buffers['program1']) > 100:
                    self.output_buffers['program1'] = self.output_buffers['program1'][-50:]

                updated = True
        except queue.Empty:
            pass

        # 处理程序2的输出队列
        try:
            while True:
                line = self.output_queues['program2'].get_nowait()
                timestamp = time.strftime("%H:%M:%S")
                formatted_line = f"[{timestamp}] {line}"
                self.output_buffers['program2'].append(formatted_line)

                # 限制缓冲区大小
                if len(self.output_buffers['program2']) > 100:
                    self.output_buffers['program2'] = self.output_buffers['program2'][-50:]

                updated = True
        except queue.Empty:
            pass

        # 只有当有新输出时才重绘输出窗口
        if updated:
            self.draw_output_window(self.overall_win, "OVERALL INFO", self.output_buffers['program0'])
            self.draw_output_window(self.output1_win, "PROGRAM 1 OUTPUT", self.output_buffers['program1'])
            self.draw_output_window(self.output2_win, "PROGRAM 2 OUTPUT", self.output_buffers['program2'])
            
    def start_program(self, program_name: str):
        """启动子程序"""
        if self.processes[program_name] and self.processes[program_name].poll() is None:
            self.output_queues[program_name].put("Program already running")
            return
            
        try:
            # 运行真实的demo程序文件
            if program_name == 'program1':
                # 运行demo_program1.py - 系统监控程序
                cmd = [sys.executable, '-u', 'demo_program1.py']  # -u 参数确保无缓冲输出
            else:
                # 运行demo_program2.py - 日志生成器
                cmd = [sys.executable, '-u', 'demo_program2.py']  # -u 参数确保无缓冲输出

            # 设置环境变量确保无缓冲输出
            env = os.environ.copy()
            env['PYTHONUNBUFFERED'] = '1'

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=0,  # 无缓冲
                env=env
            )
            self.processes[program_name] = process
            
            # 启动输出读取线程
            thread = threading.Thread(
                target=self.read_process_output,
                args=(program_name, process),
                daemon=True
            )
            thread.start()
            
            self.output_queues['program0'].put(f"{program_name} started successfully")

        except Exception as e:
            self.output_queues['program0'].put(f"{program_name} start failed: {str(e)}")

    def start_program_in_new_window(self, program_name: str):
        """在新窗口中启动程序"""
        if self.processes[program_name] and self.processes[program_name].poll() is None:
            self.output_queues['program0'].put(f"{program_name} already running in separate window")
            return

        try:
            # 根据程序名称创建不同的程序
            if program_name == 'program3':
                # 程序3: 网络监控模拟
                script_content = '''
import time
import random
import datetime
import sys

print("=== Network Monitor (Program 3) ===")
print("Running in separate window")
print("Press Ctrl+C to exit")
print()

try:
    counter = 0
    while True:
        counter += 1
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # 模拟网络数据
        download_speed = random.randint(50, 1000)
        upload_speed = random.randint(10, 100)
        ping = random.randint(10, 50)
        packets_sent = counter * 10
        packets_received = packets_sent - random.randint(0, 2)

        print(f"[{timestamp}] Network Status #{counter}")
        print(f"Download: {download_speed} KB/s | Upload: {upload_speed} KB/s")
        print(f"Ping: {ping}ms | Packets: {packets_received}/{packets_sent}")
        print("-" * 50)

        time.sleep(3)

except KeyboardInterrupt:
    print("\\nNetwork Monitor stopped by user")
except Exception as e:
    print(f"Error: {e}")
'''
                window_title = "Network Monitor - Program 3"

            elif program_name == 'program4':
                # 程序4: 文件监控模拟
                script_content = '''
import time
import random
import datetime
import sys
import os

print("=== File Monitor (Program 4) ===")
print("Running in separate window")
print("Press Ctrl+C to exit")
print()

try:
    counter = 0
    file_types = [".txt", ".log", ".dat", ".tmp", ".bak"]
    operations = ["Created", "Modified", "Deleted", "Accessed"]

    while True:
        counter += 1
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # 模拟文件操作
        filename = f"file_{random.randint(1000, 9999)}{random.choice(file_types)}"
        operation = random.choice(operations)
        size = random.randint(1, 1000)

        print(f"[{timestamp}] File Event #{counter}")
        print(f"File: {filename}")
        print(f"Operation: {operation}")
        print(f"Size: {size} KB")
        print(f"Path: C:\\\\temp\\\\monitor\\\\{filename}")
        print("-" * 50)

        time.sleep(2)

except KeyboardInterrupt:
    print("\\nFile Monitor stopped by user")
except Exception as e:
    print(f"Error: {e}")
'''
                window_title = "File Monitor - Program 4"
            else:
                return

            # 创建临时脚本文件
            script_filename = f"temp_{program_name}.py"
            with open(script_filename, 'w', encoding='utf-8') as f:
                f.write(script_content)

            # 在新窗口中启动程序
            if os.name == 'nt':  # Windows
                # 使用start命令在新窗口中运行
                cmd = f'start "{window_title}" cmd /k "python {script_filename} & pause & del {script_filename}"'
            else:  # Linux/Mac
                # 使用gnome-terminal或xterm
                cmd = f'gnome-terminal --title="{window_title}" -- bash -c "python3 {script_filename}; read -p \\"Press Enter to exit...\\"; rm {script_filename}"'

            process = subprocess.Popen(
                cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            self.processes[program_name] = process
            self.output_queues['program0'].put(f"{program_name} started in new window: {window_title}")

        except Exception as e:
            self.output_queues['program0'].put(f"Failed to start {program_name} in new window: {str(e)}")

    def read_process_output(self, program_name: str, process):
        """读取子程序输出"""
        try:
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                self.output_queues[program_name].put(line.strip())
        except Exception as e:
            self.output_queues[program_name].put(f"Read error: {str(e)}")
        finally:
            if process.stdout:
                process.stdout.close()
            
    def stop_program(self, program_name: str):
        """停止子程序"""
        if self.processes[program_name] and self.processes[program_name].poll() is None:
            self.processes[program_name].terminate()
            self.output_queues['program0'].put(f"{program_name} terminated")
        else:
            self.output_queues['program0'].put(f"{program_name} not running")
            
    def clear_output(self, program_name: str):
        """清空输出缓冲区"""
        self.output_buffers[program_name].clear()
        self.output_queues['program0'].put(f"{program_name} output cleared")

        # 强制清空并重绘对应的输出窗口
        if program_name == 'program1':
            self.force_clear_window(self.output1_win, "PROGRAM 1 OUTPUT")
        elif program_name == 'program2':
            self.force_clear_window(self.output2_win, "PROGRAM 2 OUTPUT")
        elif program_name == 'program0':
            self.force_clear_window(self.overall_win, "OVERALL INFO")

    def force_clear_window(self, win, title):
        """强制清空窗口，确保没有残留"""
        try:
            # 获取窗口尺寸
            max_y, max_x = win.getmaxyx()

            # 彻底清空：先清除，再用空格填充，再清除
            win.clear()
            win.erase()

            # 用空格填充整个窗口区域，确保清除所有残留字符
            for y in range(max_y):
                for x in range(max_x):
                    try:
                        win.addch(y, x, ' ')
                    except:
                        pass

            # 再次清空
            win.clear()
            win.erase()

            # 重绘边框和标题
            win.box()
            self.safe_addstr(win, 0, 2, f" {title} ", curses.color_pair(1))

            # 多次刷新确保显示
            win.noutrefresh()
            curses.doupdate()
            win.refresh()

        except Exception:
            # 如果上述方法失败，使用简单清空
            try:
                win.clear()
                win.box()
                self.safe_addstr(win, 0, 2, f" {title} ", curses.color_pair(1))
                win.refresh()
            except:
                pass
        
    def show_system_info(self):
        """显示系统信息"""
        import platform
        info = [
            f"System: {platform.system()} {platform.release()}",
            f"Python: {platform.python_version()}",
            f"Machine: {platform.machine()}",
            f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}"
        ]
        
        for line in info:
            self.output_queues['program0'].put(line)

    def handle_menu_action(self):
        """处理菜单选择"""
        try:
            action = self.menu_items[self.current_menu]

            if action == "Start Program 1":
                self.start_program('program1')
            elif action == "Start Program 2":
                self.start_program('program2')
            elif action == "Stop Program 1":
                self.stop_program('program1')
            elif action == "Stop Program 2":
                self.stop_program('program2')
            elif action == "Clear Output 1":
                self.clear_output('program1')
            elif action == "Clear Output 2":
                self.clear_output('program2')
            elif action == "Start Program 3 (New Window)":
                self.start_program_in_new_window('program3')
            elif action == "Start Program 4 (New Window)":
                self.start_program_in_new_window('program4')
            elif action == "Stop Program 3":
                self.stop_program('program3')
            elif action == "Stop Program 4":
                self.stop_program('program4')
            elif action == "System Info":
                self.show_system_info()
            elif action == "Exit":
                self.running = False

            # 重绘状态栏以反映变化
            self.draw_status()

        except Exception as e:
            self.output_queues['program0'].put(f"Menu error: {e}")

    def run(self):
        """主运行循环"""
        try:
            self.stdscr = curses.initscr()

            # 设置信号处理（如果支持）
            try:
                signal.signal(signal.SIGWINCH, self.handle_resize)
            except:
                # Windows可能不支持SIGWINCH
                pass

            # 初始化curses
            curses.noecho()
            curses.cbreak()
            self.stdscr.keypad(True)
            curses.curs_set(0)  # 隐藏光标
            self.stdscr.timeout(200)  # 设置较长的超时，减少刷新频率

            self.init_colors()
            self.setup_windows()

            # 初始绘制界面
            self.draw_title()
            self.draw_menu()
            self.draw_output_window(self.overall_win, "OVERALL INFO", self.output_buffers['program0'])
            self.draw_output_window(self.output1_win, "PROGRAM 1 OUTPUT", self.output_buffers['program1'])
            self.draw_output_window(self.output2_win, "PROGRAM 2 OUTPUT", self.output_buffers['program2'])
            self.draw_status()
            self.stdscr.refresh()

            # 强制刷新状态栏
            self.draw_status()

            # 添加欢迎消息
            self.output_queues['program0'].put("BIOS Terminal System Ready")
            self.output_queues['program0'].put("OVERALL Info Window Active")
            self.output_queues['program1'].put("Program 1 Window Ready")
            self.output_queues['program2'].put("Program 2 Window Ready")

            last_menu = -1  # 跟踪菜单变化
            refresh_counter = 0  # 强制刷新计数器

            while self.running:
                try:
                    # 检查终端大小是否改变
                    terminal_resized = self.check_terminal_resize()

                    if terminal_resized:
                        # 如果发生了大小调整，重置菜单状态并跳过其他重绘
                        last_menu = self.current_menu
                        continue

                    # 更新输出
                    self.update_outputs()

                    # 增加刷新计数器
                    refresh_counter += 1

                    # 如果菜单选择改变或每10次循环，重绘菜单
                    if last_menu != self.current_menu or refresh_counter % 10 == 0:
                        self.draw_menu()
                        last_menu = self.current_menu

                    # 定期更新状态栏 (每5次循环)
                    if refresh_counter % 5 == 0:
                        self.draw_status()

                    # 如果需要重绘其他内容
                    if self.need_redraw:
                        self.draw_title()
                        self.draw_output_window(self.overall_win, "OVERALL INFO", self.output_buffers['program0'])
                        self.draw_output_window(self.output1_win, "PROGRAM 1 OUTPUT", self.output_buffers['program1'])
                        self.draw_output_window(self.output2_win, "PROGRAM 2 OUTPUT", self.output_buffers['program2'])
                        self.draw_status()
                        self.stdscr.refresh()
                        self.need_redraw = False

                    # 处理键盘输入
                    key = self.stdscr.getch()

                    if key == curses.KEY_UP:
                        self.current_menu = (self.current_menu - 1) % len(self.menu_items)
                    elif key == curses.KEY_DOWN:
                        self.current_menu = (self.current_menu + 1) % len(self.menu_items)
                    elif key == ord('\n') or key == ord('\r'):
                        self.handle_menu_action()
                    elif key == 27:  # ESC键
                        self.running = False
                    elif key == ord('q') or key == ord('Q'):
                        self.running = False
                    elif key == ord('r') or key == ord('R'):
                        # 手动刷新功能
                        self.force_full_redraw()
                    elif key == curses.KEY_RESIZE:
                        # 处理终端大小改变事件
                        self.check_terminal_resize()

                except KeyboardInterrupt:
                    self.running = False
                except Exception as e:
                    # 记录错误但继续运行
                    try:
                        self.output_queues['program0'].put(f"Error: {e}")
                    except:
                        pass

        except Exception as e:
            print(f"Fatal error: {e}")
        finally:
            # 清理资源
            try:
                for process in self.processes.values():
                    if process and process.poll() is None:
                        process.terminate()
            except:
                pass
            try:
                curses.endwin()
            except:
                pass


def main():
    """主函数"""
    # 检查终端大小
    try:
        # 在Windows上设置控制台编码
        if os.name == 'nt':
            os.system('chcp 65001 >nul')

        print("Starting Stable BIOS Terminal...")
        print("Make sure your terminal is at least 80x15 characters")
        print("This version fixes the flickering issue!")
        time.sleep(2)

        terminal = StableBIOSTerminal()
        terminal.run()

        print("BIOS Terminal exited.")

    except KeyboardInterrupt:
        print("\nProgram interrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have installed: pip install windows-curses")
        print("And your terminal is large enough (at least 80x15)")


if __name__ == "__main__":
    main()
