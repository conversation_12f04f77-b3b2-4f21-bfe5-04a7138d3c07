#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Retro BIOS Style Terminal Launcher
复古BIOS风格终端启动器 - 更接近原始start.bat的界面
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import os
import sys
import time
import threading
from datetime import datetime

class RetroBIOSLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BIOS Terminal Launcher")
        self.root.geometry("640x480")
        self.root.resizable(False, False)
        
        # 经典BIOS配色方案
        self.colors = {
            'bg': '#000080',           # BIOS蓝色背景
            'text_title': '#FFFF00',   # 黄色标题
            'text_main': '#FFFFFF',    # 白色主文字
            'text_info': '#00FFFF',    # 青色信息
            'text_success': '#00FF00', # 绿色成功
            'text_error': '#FF0000',   # 红色错误
            'text_warning': '#FFAA00', # 橙色警告
            'border': '#FFFFFF',       # 白色边框
            'button_bg': '#0000AA',    # 按钮背景
            'button_active': '#FFFFFF' # 按钮激活
        }
        
        self.terminal_process = None
        self.is_running = False
        self.start_time = None
        self.animation_chars = ['|', '/', '-', '\\']
        self.animation_index = 0
        
        self.setup_window()
        self.create_widgets()
        self.start_animations()
        
    def setup_window(self):
        """设置窗口样式"""
        self.root.configure(bg=self.colors['bg'])
        
        # 居中显示
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 640) // 2
        y = (screen_height - 480) // 2
        self.root.geometry(f"640x480+{x}+{y}")
        
    def create_widgets(self):
        """创建BIOS风格界面"""
        # 顶部标题区域
        self.create_header()
        
        # 主要信息显示区域
        self.create_main_area()
        
        # 控制按钮区域
        self.create_control_area()
        
        # 状态栏
        self.create_status_bar()
        
    def create_header(self):
        """创建头部区域"""
        header_frame = tk.Frame(self.root, bg=self.colors['bg'])
        header_frame.pack(fill=tk.X, pady=10)
        
        # ASCII艺术标题
        title_text = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                          BIOS TERMINAL LAUNCHER                             ║
║                              Version 2.0                                    ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        
        title_label = tk.Label(header_frame, text=title_text,
                              bg=self.colors['bg'], fg=self.colors['text_title'],
                              font=('Courier New', 10, 'bold'),
                              justify=tk.CENTER)
        title_label.pack()
        
    def create_main_area(self):
        """创建主要显示区域"""
        main_frame = tk.Frame(self.root, bg=self.colors['bg'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 系统信息框
        info_box = tk.Frame(main_frame, bg=self.colors['bg'], 
                           highlightbackground=self.colors['border'],
                           highlightthickness=1)
        info_box.pack(fill=tk.X, pady=(0, 20))
        
        info_title = tk.Label(info_box, text="SYSTEM INFORMATION",
                             bg=self.colors['bg'], fg=self.colors['text_title'],
                             font=('Courier New', 10, 'bold'))
        info_title.pack(pady=5)
        
        # 系统信息内容
        self.create_system_info(info_box)
        
        # 状态显示框
        status_box = tk.Frame(main_frame, bg=self.colors['bg'],
                             highlightbackground=self.colors['border'],
                             highlightthickness=1)
        status_box.pack(fill=tk.X, pady=(0, 20))
        
        status_title = tk.Label(status_box, text="TERMINAL STATUS",
                               bg=self.colors['bg'], fg=self.colors['text_title'],
                               font=('Courier New', 10, 'bold'))
        status_title.pack(pady=5)
        
        # 状态信息
        self.status_display = tk.Label(status_box, 
                                      text="Ready to start BIOS Terminal",
                                      bg=self.colors['bg'], fg=self.colors['text_main'],
                                      font=('Courier New', 9))
        self.status_display.pack(pady=5)
        
        self.runtime_display = tk.Label(status_box, text="Runtime: 00:00:00",
                                       bg=self.colors['bg'], fg=self.colors['text_info'],
                                       font=('Courier New', 9))
        self.runtime_display.pack(pady=2)
        
        # 动画指示器
        self.animation_display = tk.Label(status_box, text="",
                                         bg=self.colors['bg'], fg=self.colors['text_success'],
                                         font=('Courier New', 12, 'bold'))
        self.animation_display.pack(pady=5)
        
    def create_system_info(self, parent):
        """创建系统信息显示"""
        try:
            import platform
            
            info_text = f"""
Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
System: {platform.system()} {platform.release()}
Python Version: {platform.python_version()}
Machine: {platform.machine()}
Terminal Size Required: At least 80x15 characters
            """.strip()
            
            info_label = tk.Label(parent, text=info_text,
                                 bg=self.colors['bg'], fg=self.colors['text_main'],
                                 font=('Courier New', 8),
                                 justify=tk.LEFT)
            info_label.pack(pady=5)
            
        except Exception as e:
            error_label = tk.Label(parent, text=f"Error getting system info: {e}",
                                  bg=self.colors['bg'], fg=self.colors['text_error'],
                                  font=('Courier New', 8))
            error_label.pack(pady=5)
            
    def create_control_area(self):
        """创建控制区域"""
        control_frame = tk.Frame(self.root, bg=self.colors['bg'])
        control_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 按钮容器
        button_container = tk.Frame(control_frame, bg=self.colors['bg'])
        button_container.pack()
        
        # 主要控制按钮
        self.start_btn = self.create_bios_button(button_container, 
                                                "START BIOS TERMINAL", 
                                                self.start_terminal)
        self.start_btn.pack(side=tk.LEFT, padx=10)
        
        self.stop_btn = self.create_bios_button(button_container, 
                                               "STOP TERMINAL", 
                                               self.stop_terminal)
        self.stop_btn.pack(side=tk.LEFT, padx=10)
        self.stop_btn.config(state=tk.DISABLED)
        
        self.exit_btn = self.create_bios_button(button_container, 
                                               "EXIT LAUNCHER", 
                                               self.exit_app)
        self.exit_btn.pack(side=tk.LEFT, padx=10)
        
    def create_bios_button(self, parent, text, command):
        """创建BIOS风格按钮"""
        btn = tk.Button(parent, text=text,
                       bg=self.colors['button_bg'], fg=self.colors['text_main'],
                       font=('Courier New', 10, 'bold'),
                       width=18, height=2,
                       command=command,
                       relief=tk.RAISED, bd=3,
                       activebackground=self.colors['button_active'],
                       activeforeground=self.colors['bg'])
        
        # 添加悬停效果
        def on_enter(e):
            if btn['state'] != tk.DISABLED:
                btn.config(bg=self.colors['text_main'], fg=self.colors['bg'])
                
        def on_leave(e):
            if btn['state'] != tk.DISABLED:
                btn.config(bg=self.colors['button_bg'], fg=self.colors['text_main'])
                
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        
        return btn
        
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, bg=self.colors['bg'],
                               highlightbackground=self.colors['border'],
                               highlightthickness=1)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 左侧状态文字
        self.status_text = tk.Label(status_frame, 
                                   text="Ready - Click START BIOS TERMINAL to begin",
                                   bg=self.colors['bg'], fg=self.colors['text_info'],
                                   font=('Courier New', 8))
        self.status_text.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 右侧时间显示
        self.time_display = tk.Label(status_frame, text="",
                                    bg=self.colors['bg'], fg=self.colors['text_warning'],
                                    font=('Courier New', 8, 'bold'))
        self.time_display.pack(side=tk.RIGHT, padx=10, pady=5)
        
    def start_terminal(self):
        """启动BIOS终端"""
        try:
            if not os.path.exists("stable_bios_terminal.py"):
                messagebox.showerror("Error", 
                                   "stable_bios_terminal.py not found!\n\n" +
                                   "Please make sure the file is in the same directory.")
                return
                
            # 更新状态显示
            self.status_display.config(text="Starting Stable BIOS Terminal...",
                                      fg=self.colors['text_warning'])
            self.status_text.config(text="Initializing terminal process...")
            self.root.update()
            
            # 模拟原始start.bat的输出
            time.sleep(1)  # 短暂延迟，模拟启动过程
            
            # 启动终端程序
            self.terminal_process = subprocess.Popen([sys.executable, "stable_bios_terminal.py"])
            
            self.is_running = True
            self.start_time = datetime.now()
            
            # 更新界面状态
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            self.status_display.config(text="BIOS Terminal is running",
                                      fg=self.colors['text_success'])
            self.status_text.config(text="Terminal active - Use terminal window for control")
            
            # 启动监控
            self.monitor_process()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start terminal:\n{e}")
            self.status_display.config(text="Failed to start terminal",
                                      fg=self.colors['text_error'])
            self.status_text.config(text="Startup failed - Check error message")
            
    def stop_terminal(self):
        """停止BIOS终端"""
        try:
            if self.terminal_process:
                self.terminal_process.terminate()
                self.terminal_process = None
                
            self.is_running = False
            self.start_time = None
            
            # 更新界面状态
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.status_display.config(text="Terminal stopped",
                                      fg=self.colors['text_warning'])
            self.status_text.config(text="Terminal stopped by user")
            self.runtime_display.config(text="Runtime: 00:00:00")
            self.animation_display.config(text="")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop terminal:\n{e}")
            
    def monitor_process(self):
        """监控进程状态"""
        if self.is_running and self.terminal_process:
            if self.terminal_process.poll() is not None:
                # 进程已结束
                self.stop_terminal()
                self.status_display.config(text="Terminal exited",
                                          fg=self.colors['text_info'])
                self.status_text.config(text="Terminal process ended normally")
                return
                
        # 每秒检查一次
        if self.is_running:
            self.root.after(1000, self.monitor_process)
            
    def start_animations(self):
        """启动动画效果"""
        self.update_time()
        self.update_runtime()
        self.update_animation()
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_display.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def update_runtime(self):
        """更新运行时间"""
        if self.is_running and self.start_time:
            runtime = datetime.now() - self.start_time
            hours, remainder = divmod(int(runtime.total_seconds()), 3600)
            minutes, seconds = divmod(remainder, 60)
            runtime_str = f"Runtime: {hours:02d}:{minutes:02d}:{seconds:02d}"
            self.runtime_display.config(text=runtime_str)
            
        self.root.after(1000, self.update_runtime)
        
    def update_animation(self):
        """更新动画指示器"""
        if self.is_running:
            char = self.animation_chars[self.animation_index]
            self.animation_display.config(text=f"Running {char}")
            self.animation_index = (self.animation_index + 1) % len(self.animation_chars)
        else:
            self.animation_display.config(text="")
            
        self.root.after(250, self.update_animation)
        
    def exit_app(self):
        """退出应用程序"""
        if self.is_running:
            result = messagebox.askyesno("Confirm Exit", 
                                       "Terminal is still running. Stop it and exit?")
            if result:
                self.stop_terminal()
                self.root.destroy()
        else:
            self.root.destroy()
            
    def run(self):
        """运行GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.exit_app)
        self.root.mainloop()

def main():
    """主函数"""
    try:
        launcher = RetroBIOSLauncher()
        launcher.run()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
