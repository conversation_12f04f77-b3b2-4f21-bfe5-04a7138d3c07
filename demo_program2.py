#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例程序2 - 日志生成器
"""

import time
import random
import datetime

def main():
    """模拟日志生成程序"""
    print("=== 日志生成器启动 ===", flush=True)
    
    log_levels = ["INFO", "WARNING", "ERROR", "DEBUG"]
    services = ["WebServer", "Database", "Cache", "Auth", "API"]
    
    counter = 0
    while True:
        counter += 1
        
        # 随机生成日志
        level = random.choice(log_levels)
        service = random.choice(services)
        
        # 根据日志级别生成不同的消息
        if level == "INFO":
            messages = [
                "服务正常运行",
                "用户登录成功",
                "数据同步完成",
                "缓存更新",
                "定时任务执行"
            ]
        elif level == "WARNING":
            messages = [
                "连接超时，正在重试",
                "内存使用率较高",
                "响应时间较慢",
                "磁盘空间不足",
                "网络延迟增加"
            ]
        elif level == "ERROR":
            messages = [
                "数据库连接失败",
                "文件读取错误",
                "认证失败",
                "服务不可用",
                "配置文件错误"
            ]
        else:  # DEBUG
            messages = [
                "调试信息输出",
                "变量值检查",
                "函数调用跟踪",
                "性能分析数据",
                "内部状态检查"
            ]
        
        message = random.choice(messages)
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"[{timestamp}] [{level:7}] {service}: {message}", flush=True)
        
        # 随机延迟
        delay = random.uniform(0.5, 3.0)
        time.sleep(delay)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("日志生成器已停止")
    except Exception as e:
        print(f"程序出错: {e}")
