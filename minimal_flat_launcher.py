#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minimal Flat Launcher
极简扁平化启动器 - 完全模拟start.bat的布局和流程
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import os
import sys
from datetime import datetime

class MinimalFlatLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BIOS Terminal")
        self.root.geometry("480x240")
        self.root.resizable(False, False)
        
        # 现代扁平化配色
        self.colors = {
            'bg': '#1E1E1E',           # VS Code深色背景
            'text_primary': '#FFFFFF', # 纯白文字
            'text_secondary': '#CCCCCC', # 浅灰文字
            'accent': '#007ACC',       # VS Code蓝色
            'success': '#4CAF50',      # Material绿色
            'button_bg': '#007ACC',    # 按钮背景
            'button_hover': '#005A9E'  # 按钮悬停
        }
        
        self.terminal_process = None
        
        self.setup_window()
        self.create_start_bat_layout()
        
    def setup_window(self):
        """设置窗口样式"""
        self.root.configure(bg=self.colors['bg'])
        
        # 居中显示
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 480) // 2
        y = (screen_height - 240) // 2
        self.root.geometry(f"480x240+{x}+{y}")
        
    def create_start_bat_layout(self):
        """创建完全模拟start.bat的布局"""
        # 主容器
        container = tk.Frame(self.root, bg=self.colors['bg'])
        container.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # 第1行: "Starting Stable BIOS Terminal (No Flickering)..."
        line1 = tk.Label(container, 
                        text="Starting Stable BIOS Terminal (No Flickering)...",
                        bg=self.colors['bg'], 
                        fg=self.colors['text_primary'],
                        font=('Consolas', 11),
                        anchor='w')
        line1.pack(fill=tk.X, pady=(0, 10))
        
        # 第2行: "Make sure your terminal window is large enough (at least 80x15)"
        line2 = tk.Label(container, 
                        text="Make sure your terminal window is large enough (at least 80x15)",
                        bg=self.colors['bg'], 
                        fg=self.colors['text_secondary'],
                        font=('Consolas', 10),
                        anchor='w')
        line2.pack(fill=tk.X, pady=(0, 20))
        
        # 第3行: 空行（在start.bat中是echo.）
        spacer = tk.Frame(container, bg=self.colors['bg'], height=10)
        spacer.pack(fill=tk.X)
        
        # 第4行: 启动按钮（替代"python stable_bios_terminal.py"）
        self.start_button = tk.Button(container,
                                     text="▶ python stable_bios_terminal.py",
                                     command=self.start_terminal,
                                     bg=self.colors['button_bg'],
                                     fg='white',
                                     font=('Consolas', 11, 'bold'),
                                     relief=tk.FLAT,
                                     bd=0,
                                     height=2,
                                     cursor='hand2')
        self.start_button.pack(fill=tk.X, pady=(0, 20))
        
        # 添加按钮悬停效果
        self.start_button.bind("<Enter>", lambda e: self.start_button.config(bg=self.colors['button_hover']))
        self.start_button.bind("<Leave>", lambda e: self.start_button.config(bg=self.colors['button_bg']))
        
        # 第5行: 状态显示（替代"pause"）
        self.status_label = tk.Label(container, 
                                    text="Press button to continue...",
                                    bg=self.colors['bg'], 
                                    fg=self.colors['text_secondary'],
                                    font=('Consolas', 10),
                                    anchor='w')
        self.status_label.pack(fill=tk.X, pady=(0, 10))
        
        # 底部信息
        info_frame = tk.Frame(container, bg=self.colors['bg'])
        info_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 时间显示
        self.time_label = tk.Label(info_frame, 
                                  text="",
                                  bg=self.colors['bg'], 
                                  fg=self.colors['accent'],
                                  font=('Consolas', 9))
        self.time_label.pack(side=tk.RIGHT)
        
        # 版本信息
        version_label = tk.Label(info_frame, 
                                text="BIOS Terminal Launcher",
                                bg=self.colors['bg'], 
                                fg=self.colors['text_secondary'],
                                font=('Consolas', 8))
        version_label.pack(side=tk.LEFT)
        
        # 启动时间更新
        self.update_time()
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def start_terminal(self):
        """启动BIOS终端 - 完全模拟start.bat的行为"""
        try:
            if not os.path.exists("stable_bios_terminal.py"):
                messagebox.showerror("Error", "stable_bios_terminal.py not found!")
                return
                
            # 更新状态显示
            self.status_label.config(text="Launching BIOS Terminal...", 
                                   fg=self.colors['accent'])
            self.start_button.config(text="⏳ Launching...", state=tk.DISABLED, 
                                   bg=self.colors['text_secondary'])
            self.root.update()
            
            # 启动终端程序（对应start.bat中的python命令）
            self.terminal_process = subprocess.Popen([sys.executable, "stable_bios_terminal.py"])
            
            # 更新为运行状态
            self.start_button.config(text="✓ BIOS Terminal Running", 
                                   bg=self.colors['success'])
            self.status_label.config(text="BIOS Terminal is running. Use the terminal window for control.", 
                                   fg=self.colors['success'])
            
            # 监控进程状态
            self.monitor_terminal()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start terminal:\n{e}")
            self.reset_button()
            
    def monitor_terminal(self):
        """监控终端进程"""
        if self.terminal_process and self.terminal_process.poll() is None:
            # 进程仍在运行，继续监控
            self.root.after(1000, self.monitor_terminal)
        else:
            # 进程已结束，模拟start.bat的pause行为
            self.terminal_process = None
            self.start_button.config(text="🔄 BIOS Terminal Exited - Click to Restart", 
                                   state=tk.NORMAL,
                                   bg=self.colors['button_bg'])
            self.status_label.config(text="BIOS Terminal exited. Press button to continue...", 
                                   fg=self.colors['text_secondary'])
            
            # 重新绑定悬停效果
            self.start_button.bind("<Enter>", lambda e: self.start_button.config(bg=self.colors['button_hover']))
            self.start_button.bind("<Leave>", lambda e: self.start_button.config(bg=self.colors['button_bg']))
            
    def reset_button(self):
        """重置按钮状态"""
        self.start_button.config(text="▶ python stable_bios_terminal.py", 
                               state=tk.NORMAL,
                               bg=self.colors['button_bg'])
        self.status_label.config(text="Press button to continue...", 
                               fg=self.colors['text_secondary'])
        
    def run(self):
        """运行GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """程序关闭时清理"""
        if self.terminal_process:
            self.terminal_process.terminate()
        self.root.destroy()

def main():
    """主函数"""
    try:
        launcher = MinimalFlatLauncher()
        launcher.run()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
